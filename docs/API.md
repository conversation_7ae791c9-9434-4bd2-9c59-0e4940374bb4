# MoodTracker API 文档

## 概述

MoodTracker API 是一个情侣心情记录和同步系统，支持用户注册、情侣匹配、心情记录和数据同步功能。

## 基础信息

- **Base URL**: `http://localhost:8080`
- **API Version**: v1
- **Content-Type**: `application/json`
- **认证方式**: <PERSON><PERSON> (JWT)

## 通用响应格式

所有API响应都遵循统一格式：

```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "error": null,
  "timestamp": "2025-08-03T12:00:00Z"
}
```

## 错误响应

```json
{
  "success": false,
  "message": null,
  "data": null,
  "error": "错误描述",
  "timestamp": "2025-08-03T12:00:00Z"
}
```

## 认证相关 API

### 用户注册

**POST** `/api/v1/auth/register`

**请求体**:
```json
{
  "username": "testuser",
  "password": "password123",
  "nickname": "测试用户"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "userId": "uuid",
      "username": "testuser",
      "nickname": "测试用户",
      "avatarUrl": null,
      "createdAt": "2025-08-03T12:00:00Z",
      "lastLoginAt": null
    },
    "accessToken": "jwt_token",
    "refreshToken": "refresh_token",
    "expiresAt": "2025-08-03T13:00:00Z"
  }
}
```

### 用户登录

**POST** `/api/v1/auth/login`

**请求体**:
```json
{
  "username": "testuser",
  "password": "password123",
  "deviceId": "550e8400-e29b-41d4-a716-************"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "userId": "uuid",
      "username": "testuser",
      "nickname": "测试用户",
      "avatarUrl": null,
      "createdAt": "2025-08-03T12:00:00Z",
      "lastLoginAt": "2025-08-03T13:00:00Z"
    },
    "accessToken": "jwt_token",
    "refreshToken": "refresh_token",
    "expiresAt": "2025-08-03T13:00:00Z"
  }
}
```

### 刷新令牌

**POST** `/api/v1/auth/refresh-token`

**请求头**:
```
Authorization: Bearer <refresh_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "accessToken": "new_jwt_token",
    "refreshToken": "new_refresh_token",
    "expiresAt": "2025-08-03T14:00:00Z"
  }
}
```

### 用户登出

**POST** `/api/v1/auth/logout`

**请求头**:
```
Authorization: Bearer <refresh_token>
```

**响应**:
```json
{
  "success": true,
  "message": "登出成功",
  "data": null
}
```

### 获取当前用户信息

**GET** `/api/v1/user/me`

**请求头**:
```
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "userId": "uuid",
    "username": "testuser"
  }
}
```

## 情侣关系 API

### 创建情侣关系

**POST** `/api/v1/couples/create`

**请求头**:
```
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "relationshipName": "我们的小日子"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "coupleId": "uuid",
    "matchCode": "12345678",
    "user1": {
      "userId": "uuid",
      "username": "user1",
      "nickname": "用户1",
      "avatarUrl": null,
      "createdAt": "2025-08-03T12:00:00Z",
      "lastLoginAt": "2025-08-03T13:00:00Z"
    },
    "user2": null,
    "relationshipName": "我们的小日子",
    "status": "waiting",
    "createdAt": "2025-08-03T12:00:00Z",
    "matchedAt": null
  }
}
```

### 加入情侣关系

**POST** `/api/v1/couples/join`

**请求头**:
```
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "matchCode": "12345678"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "coupleId": "uuid",
    "matchCode": null,
    "user1": {
      "userId": "uuid",
      "username": "user1",
      "nickname": "用户1",
      "avatarUrl": null,
      "createdAt": "2025-08-03T12:00:00Z",
      "lastLoginAt": "2025-08-03T13:00:00Z"
    },
    "user2": {
      "userId": "uuid",
      "username": "user2",
      "nickname": "用户2",
      "avatarUrl": null,
      "createdAt": "2025-08-03T12:00:00Z",
      "lastLoginAt": "2025-08-03T13:00:00Z"
    },
    "relationshipName": "我们的小日子",
    "status": "matched",
    "createdAt": "2025-08-03T12:00:00Z",
    "matchedAt": "2025-08-03T12:30:00Z"
  }
}
```

### 获取情侣信息

**GET** `/api/v1/couples/info`

**请求头**:
```
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "coupleId": "uuid",
    "matchCode": null,
    "user1": {
      "userId": "uuid",
      "username": "user1",
      "nickname": "用户1",
      "avatarUrl": null,
      "createdAt": "2025-08-03T12:00:00Z",
      "lastLoginAt": "2025-08-03T13:00:00Z"
    },
    "user2": {
      "userId": "uuid",
      "username": "user2",
      "nickname": "用户2",
      "avatarUrl": null,
      "createdAt": "2025-08-03T12:00:00Z",
      "lastLoginAt": "2025-08-03T13:00:00Z"
    },
    "relationshipName": "我们的小日子",
    "status": "matched",
    "createdAt": "2025-08-03T12:00:00Z",
    "matchedAt": "2025-08-03T12:30:00Z"
  }
}
```

### 离开情侣关系

**DELETE** `/api/v1/couples/leave`

**请求头**:
```
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "message": "成功离开情侣关系",
  "data": null
}
```

## 心情记录 API

### 创建心情记录

**POST** `/api/v1/moods`

**请求头**:
```
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "date": "2025-08-03",
  "mood": 8,
  "note": "今天心情很好！"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "entryId": "uuid",
    "userId": "uuid",
    "coupleId": "uuid",
    "date": "2025-08-03",
    "mood": 8,
    "note": "今天心情很好！",
    "createdAt": "2025-08-03T12:00:00Z",
    "updatedAt": "2025-08-03T12:00:00Z",
    "syncStatus": "SYNCED",
    "isDeleted": false
  }
}
```

### 更新心情记录

**PUT** `/api/v1/moods/{entryId}`

**请求头**:
```
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "mood": 10,
  "note": "心情更好了！"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "entryId": "uuid",
    "userId": "uuid",
    "coupleId": "uuid",
    "date": "2025-08-03",
    "mood": 10,
    "note": "心情更好了！",
    "createdAt": "2025-08-03T12:00:00Z",
    "updatedAt": "2025-08-03T12:30:00Z",
    "syncStatus": "SYNCED",
    "isDeleted": false
  }
}
```

### 删除心情记录

**DELETE** `/api/v1/moods/{entryId}`

**请求头**:
```
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "message": "心情记录删除成功",
  "data": null
}
```

### 获取情侣心情记录

**GET** `/api/v1/moods/couple`

**请求头**:
```
Authorization: Bearer <access_token>
```

**查询参数**:
- `startDate`: 开始日期 (YYYY-MM-DD)
- `endDate`: 结束日期 (YYYY-MM-DD)
- `limit`: 限制数量 (默认100，最大1000)
- `offset`: 偏移量 (默认0)

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "entryId": "uuid",
      "userId": "uuid",
      "coupleId": "uuid",
      "date": "2025-08-03",
      "mood": 8,
      "note": "今天心情很好！",
      "createdAt": "2025-08-03T12:00:00Z",
      "updatedAt": "2025-08-03T12:00:00Z",
      "syncStatus": "SYNCED",
      "isDeleted": false
    }
  ]
}
```

## 数据同步 API

### 获取增量变更

**GET** `/api/v1/sync/changes`

**请求头**:
```
Authorization: Bearer <access_token>
```

**查询参数**:
- `lastSyncTime`: 最后同步时间 (RFC3339格式)

**响应**:
```json
{
  "success": true,
  "data": {
    "changes": [
      {
        "entryId": "uuid",
        "userId": "uuid",
        "coupleId": "uuid",
        "date": "2025-08-03",
        "mood": 8,
        "note": "心情记录",
        "createdAt": "2025-08-03T12:00:00Z",
        "updatedAt": "2025-08-03T12:00:00Z",
        "syncStatus": "SYNCED",
        "isDeleted": false
      }
    ],
    "lastSyncTime": "2025-08-03T12:00:00Z"
  }
}
```

### 批量上传变更

**POST** `/api/v1/sync/upload`

**请求头**:
```
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "changes": [
    {
      "entryId": "uuid",
      "date": "2025-08-03",
      "mood": 8,
      "note": "心情记录",
      "createdAt": "2025-08-03T12:00:00Z",
      "updatedAt": "2025-08-03T12:00:00Z",
      "isDeleted": false
    }
  ]
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "processed": 1,
    "conflicts": [],
    "errors": []
  }
}
```

### 获取最后同步时间

**GET** `/api/v1/sync/last-sync-time`

**请求头**:
```
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "lastSyncTime": "2025-08-03T12:00:00Z"
  }
}
```

## 状态码

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `429`: 请求过于频繁
- `500`: 服务器内部错误

## 错误代码

- `INVALID_REQUEST`: 请求参数无效
- `UNAUTHORIZED`: 未授权访问
- `INVALID_CREDENTIALS`: 用户名或密码错误
- `USER_ALREADY_EXISTS`: 用户已存在
- `TOKEN_EXPIRED`: Token已过期
- `TOKEN_INVALID`: Token无效
- `COUPLE_ALREADY_EXISTS`: 情侣关系已存在
- `INVALID_MATCH_CODE`: 匹配码无效
- `MOOD_ALREADY_EXISTS`: 今日心情记录已存在
- `INVALID_MOOD_VALUE`: 心情值无效

## 限流

- 默认限制：100请求/分钟/IP（容量100，每分钟补充10个令牌）
- 认证用户：1000请求/分钟/用户（容量1000，每分钟补充100个令牌）
- 超出限制返回429状态码
- 使用令牌桶算法实现

## 安全

- 所有密码使用bcrypt加密
- JWT令牌默认1小时过期（可通过JWT_EXPIRES_IN环境变量配置，单位：秒）
- 刷新令牌30天过期
- 支持CORS跨域请求
- 自动panic恢复
