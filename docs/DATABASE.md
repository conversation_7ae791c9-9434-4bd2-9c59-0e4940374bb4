# MoodTracker API 数据库结构文档

## 概述

MoodTracker API 使用 MySQL 8.0+ 数据库，采用关系型数据库设计，支持情侣心情记录和数据同步功能。

## 数据库配置

### 字符集和排序规则
- **字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_unicode_ci`
- **支持**: 完整的Unicode字符，包括emoji表情

### 连接配置
```sql
CREATE DATABASE moodtracker 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;
```

## 表结构设计

### 1. users - 用户表

存储用户基本信息和认证数据。

```sql
CREATE TABLE users (
    user_id VARCHAR(36) PRIMARY KEY COMMENT '用户唯一标识(UUID)',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希值',
    nickname VARCHAR(100) COMMENT '用户昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    
    INDEX idx_username (username),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='用户表';
```

**字段说明**:
- `user_id`: UUID格式的用户唯一标识
- `username`: 3-20字符，支持字母、数字、下划线
- `password_hash`: bcrypt加密的密码哈希
- `nickname`: 用户显示名称，可为空
- `avatar_url`: 头像图片URL，可为空

### 2. couples - 情侣关系表

存储情侣关系信息和匹配状态。

```sql
CREATE TABLE couples (
    couple_id VARCHAR(36) PRIMARY KEY COMMENT '情侣关系唯一标识(UUID)',
    user1_id VARCHAR(36) NOT NULL COMMENT '用户1ID(创建者)',
    user2_id VARCHAR(36) NULL COMMENT '用户2ID(加入者)',
    relationship_name VARCHAR(100) COMMENT '关系名称',
    match_code VARCHAR(8) COMMENT '8位数字匹配码',
    status ENUM('waiting', 'matched') DEFAULT 'waiting' COMMENT '关系状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    matched_at TIMESTAMP NULL COMMENT '匹配完成时间',
    expires_at TIMESTAMP NOT NULL DEFAULT (CURRENT_TIMESTAMP + INTERVAL 1 HOUR) COMMENT '匹配码过期时间',

    FOREIGN KEY (user1_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (user2_id) REFERENCES users(user_id) ON DELETE CASCADE,

    INDEX idx_match_code (match_code),
    INDEX idx_user1 (user1_id),
    INDEX idx_user2 (user2_id),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB COMMENT='情侣关系表';
```

**字段说明**:
- `couple_id`: UUID格式的关系唯一标识
- `user1_id`: 创建关系的用户ID
- `user2_id`: 加入关系的用户ID，初始为NULL
- `match_code`: 8位数字匹配码，可为NULL
- `relationship_name`: 关系名称，可为NULL
- `status`: waiting(等待匹配) / matched(已匹配)
- `expires_at`: 匹配码过期时间，默认1小时后过期

### 3. moods - 心情记录表

存储用户每日心情记录和相关信息。

```sql
CREATE TABLE moods (
    entry_id VARCHAR(36) PRIMARY KEY COMMENT '记录唯一标识(UUID)',
    user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
    couple_id VARCHAR(36) NOT NULL COMMENT '情侣关系ID',
    date DATE NOT NULL COMMENT '记录日期',
    mood INT NOT NULL COMMENT '心情值(1-12)',
    note TEXT COMMENT '心情备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    sync_status ENUM('PENDING', 'SYNCED', 'FAILED') DEFAULT 'SYNCED' COMMENT '同步状态',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除(软删除)',

    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (couple_id) REFERENCES couples(couple_id) ON DELETE CASCADE,

    UNIQUE KEY unique_user_date (user_id, date),
    INDEX idx_couple_date (couple_id, date),
    INDEX idx_updated_at (updated_at),
    INDEX idx_sync_status (sync_status),

    CONSTRAINT chk_mood_range CHECK (mood >= 1 AND mood <= 12)
) ENGINE=InnoDB COMMENT='心情记录表';
```

**字段说明**:
- `entry_id`: UUID格式的记录唯一标识
- `mood`: 1-12的心情值，1最低，12最高，使用INT类型
- `date`: 记录日期，每用户每日只能有一条记录
- `sync_status`: 同步状态，支持PENDING/SYNCED/FAILED三种状态
- `is_deleted`: 软删除标记，保护数据完整性

### 4. refresh_tokens - 刷新令牌表

存储用户的刷新令牌信息，支持多设备登录。

```sql
CREATE TABLE refresh_tokens (
    token_id VARCHAR(36) PRIMARY KEY COMMENT '令牌唯一标识(UUID)',
    user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
    token_hash VARCHAR(255) NOT NULL COMMENT '令牌哈希值',
    device_id VARCHAR(36) COMMENT '设备ID',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,

    INDEX idx_user_device (user_id, device_id),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB COMMENT='刷新令牌表';
```

**字段说明**:
- `token_hash`: SHA256哈希的令牌值
- `device_id`: 设备唯一标识，支持多设备，可为NULL
- `expires_at`: 令牌过期时间，默认30天

### 5. migrations - 数据库迁移记录表

记录数据库版本迁移历史，用于版本管理和回滚。

```sql
CREATE TABLE migrations (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    version VARCHAR(50) NOT NULL UNIQUE COMMENT '迁移版本号',
    name VARCHAR(255) NOT NULL COMMENT '迁移名称',
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间'
) ENGINE=InnoDB COMMENT='数据库迁移记录表';
```

**字段说明**:
- `id`: 自增主键
- `version`: 迁移版本号，全局唯一
- `name`: 迁移的描述性名称
- `executed_at`: 迁移执行时间

## 数据关系图

```
users (1) -----> (0..1) couples
  |                       |
  |                       |
  v                       v
refresh_tokens (n)    moods (n)
```

### 关系说明

1. **用户 -> 情侣关系**: 一个用户最多参与一个活跃的情侣关系
2. **用户 -> 刷新令牌**: 一个用户可以有多个刷新令牌（多设备）
3. **情侣关系 -> 心情记录**: 一个情侣关系包含两个用户的所有心情记录
4. **用户 -> 心情记录**: 一个用户每天最多一条心情记录

## 索引策略

### 主要索引

1. **用户查询优化**
   - `idx_username`: 登录查询
   - `idx_created_at`: 用户注册统计

2. **情侣关系优化**
   - `idx_match_code`: 匹配码查询
   - `idx_user1`, `idx_user2`: 用户关系查询
   - `idx_expires_at`: 过期清理

3. **心情记录优化**
   - `unique_user_date`: 用户日期唯一约束
   - `idx_couple_date`: 情侣记录查询
   - `idx_updated_at`: 数据同步查询
   - `idx_sync_status`: 同步状态查询

4. **令牌管理优化**
   - `idx_user_device`: 用户设备查询
   - `idx_expires_at`: 过期清理

## 数据约束

### 业务约束

1. **用户名唯一性**: 全局唯一用户名
2. **匹配码唯一性**: 全局唯一8位数字码
3. **每日心情唯一性**: 每用户每日最多一条记录
4. **心情值范围**: 1-12的有效范围

### 外键约束

1. **级联删除**: 用户删除时自动删除相关数据
2. **引用完整性**: 保证数据关系的一致性

## 数据同步设计

### 同步字段

- `updated_at`: 记录最后更新时间
- `sync_status`: 同步状态标记
- `is_deleted`: 软删除标记

### 同步策略

1. **增量同步**: 基于`updated_at`时间戳
2. **冲突处理**: 服务器版本优先
3. **软删除**: 保护数据完整性

## 性能优化

### 查询优化

1. **分页查询**: 避免大数据量查询
2. **索引覆盖**: 减少回表查询
3. **条件过滤**: 精确的WHERE条件

### 存储优化

1. **数据类型**: 选择合适的数据类型
2. **字段长度**: 合理的字段长度限制
3. **NULL值**: 合理使用NULL值

## 维护操作

### 定期清理

```sql
-- 清理过期的匹配码
DELETE FROM couples 
WHERE status = 'waiting' AND expires_at < NOW();

-- 清理过期的刷新令牌
DELETE FROM refresh_tokens 
WHERE expires_at < NOW();

-- 清理软删除的旧记录（可选）
DELETE FROM moods 
WHERE is_deleted = TRUE AND updated_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

### 数据统计

```sql
-- 用户统计
SELECT COUNT(*) as total_users FROM users;

-- 活跃情侣统计
SELECT COUNT(*) as active_couples FROM couples WHERE status = 'matched';

-- 心情记录统计
SELECT COUNT(*) as total_moods FROM moods WHERE is_deleted = FALSE;

-- 每日新增用户
SELECT DATE(created_at) as date, COUNT(*) as new_users 
FROM users 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at);
```

## 备份策略

### 备份命令

```bash
# 完整备份
mysqldump -u root -p --single-transaction --routines --triggers moodtracker > backup.sql

# 仅数据备份
mysqldump -u root -p --no-create-info --single-transaction moodtracker > data_backup.sql

# 仅结构备份
mysqldump -u root -p --no-data moodtracker > structure_backup.sql
```

### 恢复命令

```bash
# 恢复完整备份
mysql -u root -p moodtracker < backup.sql

# 恢复数据
mysql -u root -p moodtracker < data_backup.sql
```

## 安全考虑

### 数据保护

1. **密码安全**: bcrypt加密存储
2. **令牌安全**: SHA256哈希存储
3. **软删除**: 防止数据误删
4. **外键约束**: 保证数据一致性

### 访问控制

1. **专用用户**: 使用专用数据库用户
2. **最小权限**: 仅授予必要权限
3. **连接加密**: 使用SSL连接
4. **审计日志**: 记录重要操作
