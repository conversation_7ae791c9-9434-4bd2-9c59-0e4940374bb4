# MoodTracker API 测试指南

## 测试概述

MoodTracker API 包含完整的测试体系，涵盖单元测试、集成测试和API功能测试。

## 测试结构

```
MoodTrackerAPI/
├── tests/                  # API集成测试
│   └── api_test.go        # 完整的API测试套件
├── pkg/utils/             # 工具函数测试
│   ├── crypto_test.go     # 加密和验证测试
│   └── jwt_test.go        # JWT令牌测试
├── internal/middleware/   # 中间件测试
│   └── ratelimit_test.go  # 限流中间件测试
└── cmd/clean/             # 数据库清理工具
    └── main.go
```

## 运行测试

### 1. 单元测试

运行所有单元测试：
```bash
go test ./... -v
```

运行特定模块测试：
```bash
# 工具函数测试
go test ./pkg/utils/... -v

# 中间件测试
go test ./internal/middleware/... -v
```

### 2. 测试覆盖率

```bash
# 生成覆盖率报告
go test ./... -cover

# 生成详细覆盖率报告
go test ./... -coverprofile=coverage.out
go tool cover -html=coverage.out -o coverage.html
```

### 3. API集成测试

```bash
# 运行API测试套件
go test ./tests/... -v
```

## 测试环境配置

### 测试配置文件

创建 `.env.test` 文件：
```env
# 测试环境配置
SERVER_PORT=8080
SERVER_GIN_MODE=test

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=
DB_NAME=moodtracker

# JWT配置
JWT_SECRET=test-secret-key-for-testing
JWT_EXPIRES_IN=3600

# 日志配置
LOG_LEVEL=error
```

### 数据库准备

1. **清理数据库**：
```bash
go run cmd/clean/main.go
```

2. **运行迁移**：
```bash
go run cmd/migrate/main.go
```

## 测试用例

### 1. 工具函数测试

#### 加密功能测试 (`pkg/utils/crypto_test.go`)
- ✅ 密码哈希生成
- ✅ 密码验证
- ✅ 随机字符串生成
- ✅ 匹配码生成
- ✅ 令牌哈希
- ✅ 用户名验证
- ✅ 密码格式验证

#### JWT功能测试 (`pkg/utils/jwt_test.go`)
- ✅ 令牌生成
- ✅ 令牌验证
- ✅ 无效令牌处理
- ✅ 错误密钥处理
- ✅ 过期令牌处理
- ✅ 刷新令牌生成
- ✅ 过期时间获取

### 2. 中间件测试

#### 限流中间件测试 (`internal/middleware/ratelimit_test.go`)
- ✅ 令牌桶允许请求
- ✅ 令牌桶补充机制
- ✅ 令牌桶重置功能
- ✅ 内存限流器功能
- ✅ 不同键值隔离
- ✅ 限流器重置
- ✅ 默认配置验证
- ✅ 用户配置验证

### 3. API集成测试

#### 认证系统测试 (`tests/api_test.go`)
- ✅ 用户注册
- ✅ 用户登录
- ✅ 令牌刷新
- ✅ 用户登出
- ✅ 获取用户信息

#### 情侣系统测试
- ✅ 创建情侣关系
- ✅ 加入情侣关系
- ✅ 获取情侣信息
- ✅ 离开情侣关系

#### 心情记录测试
- ✅ 创建心情记录
- ✅ 更新心情记录
- ✅ 删除心情记录
- ✅ 获取情侣心情记录

#### 数据同步测试
- ✅ 获取增量变更
- ✅ 批量上传变更
- ✅ 获取最后同步时间

## 手动API测试

### 1. 健康检查

```bash
curl http://localhost:8080/health
```

**期望响应**：
```json
{
  "success": true,
  "message": "服务运行正常",
  "data": {
    "database": "connected",
    "status": "ok"
  },
  "error": null,
  "timestamp": "2025-08-03T05:02:33Z"
}
```

### 2. 用户注册

```bash
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123",
    "nickname": "测试用户"
  }'
```

### 3. 用户登录

```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123",
    "deviceId": "550e8400-e29b-41d4-a716-446655440000"
  }'
```

### 4. 创建情侣关系

```bash
curl -X POST http://localhost:8080/api/v1/couples/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <access_token>" \
  -d '{
    "relationshipName": "我们的测试关系"
  }'
```

### 5. 创建心情记录

```bash
curl -X POST http://localhost:8080/api/v1/moods \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <access_token>" \
  -d '{
    "date": "2025-08-03",
    "mood": 8,
    "note": "今天心情很好"
  }'
```

## 测试结果验证

### 最新测试结果

**单元测试结果**：
```
✅ pkg/utils 测试: 9/9 通过
✅ internal/middleware 测试: 8/8 通过
```

**API功能测试结果**：
```
✅ 健康检查: 正常
✅ 用户注册: 成功
✅ 用户登录: 成功  
✅ 创建情侣关系: 成功
✅ 数据库清理: 成功
```

## 性能测试

### 并发测试

```bash
# 使用ab工具进行并发测试
ab -n 1000 -c 10 http://localhost:8080/health

# 使用wrk工具进行压力测试
wrk -t12 -c400 -d30s http://localhost:8080/health
```

### 限流测试

```bash
# 快速发送多个请求测试限流
for i in {1..110}; do 
  curl -s http://localhost:8080/health > /dev/null
done

# 验证限流生效
curl http://localhost:8080/health
```

## 测试数据管理

### 测试数据清理

```bash
# 清理所有测试数据
go run cmd/clean/main.go
```

### 测试数据生成

```bash
# 生成测试数据（如果需要）
go run scripts/generate_test_data.go
```

## 持续集成

### GitHub Actions 配置

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: moodtracker
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-go@v2
        with:
          go-version: 1.21
      - run: go test ./... -v
```

## 测试最佳实践

### 1. 测试隔离
- 每个测试用例独立运行
- 测试前清理数据库
- 使用事务回滚

### 2. 测试数据
- 使用固定的测试数据
- 避免依赖外部服务
- 模拟时间和随机数

### 3. 错误测试
- 测试各种错误情况
- 验证错误消息
- 检查错误状态码

### 4. 性能测试
- 设置合理的超时时间
- 监控内存使用
- 检查数据库连接

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 确认用户权限

2. **测试超时**
   - 增加超时时间
   - 检查网络连接
   - 优化查询性能

3. **端口占用**
   - 杀死占用进程
   - 使用不同端口
   - 检查防火墙设置

### 调试技巧

```bash
# 启用详细日志
export LOG_LEVEL=debug

# 运行单个测试
go test -run TestSpecificFunction -v

# 使用调试器
dlv test ./tests/...
```
