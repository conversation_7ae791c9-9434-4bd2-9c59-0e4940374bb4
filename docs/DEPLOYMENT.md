# MoodTracker API 部署指南

## 环境要求

### 系统要求
- Go 1.21 或更高版本
- MySQL 8.0 或更高版本
- Linux/macOS/Windows

### 依赖包
项目使用 Go Modules 管理依赖，主要依赖包括：
- gin-gonic/gin (Web框架)
- go-sql-driver/mysql (MySQL驱动)
- golang-jwt/jwt/v5 (JWT处理)
- sirupsen/logrus (日志处理)

## 配置

### 环境变量

创建 `.env` 文件：

```env
# 服务器配置
SERVER_PORT=8080
SERVER_GIN_MODE=release

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=moodtracker

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=3600

# 日志配置
LOG_LEVEL=info
```

### 数据库配置

1. 创建数据库：
```sql
CREATE DATABASE moodtracker CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 创建用户并授权：
```sql
CREATE USER 'moodtracker'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON moodtracker.* TO 'moodtracker'@'%';
FLUSH PRIVILEGES;
```

## 构建和部署

### 本地开发

1. 克隆项目：
```bash
git clone <repository-url>
cd MoodTrackerAPI
```

2. 安装依赖：
```bash
go mod tidy
```

3. 运行数据库迁移：
```bash
go run cmd/migrate/main.go
```

4. 启动服务：
```bash
go run cmd/server/main.go
```

### 生产环境部署

1. 构建二进制文件：
```bash
# Linux
GOOS=linux GOARCH=amd64 go build -o bin/moodtracker-api cmd/server/main.go

# Windows
GOOS=windows GOARCH=amd64 go build -o bin/moodtracker-api.exe cmd/server/main.go

# macOS
GOOS=darwin GOARCH=amd64 go build -o bin/moodtracker-api cmd/server/main.go
```

2. 创建部署目录：
```bash
mkdir -p /opt/moodtracker
cp bin/moodtracker-api /opt/moodtracker/
cp .env /opt/moodtracker/
cp -r migrations /opt/moodtracker/
```

3. 运行数据库迁移：
```bash
cd /opt/moodtracker
./moodtracker-api -migrate
```

4. 创建systemd服务文件 `/etc/systemd/system/moodtracker.service`：
```ini
[Unit]
Description=MoodTracker API Server
After=network.target mysql.service

[Service]
Type=simple
User=moodtracker
Group=moodtracker
WorkingDirectory=/opt/moodtracker
ExecStart=/opt/moodtracker/moodtracker-api
Restart=always
RestartSec=5
Environment=GIN_MODE=release

[Install]
WantedBy=multi-user.target
```

5. 启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable moodtracker
sudo systemctl start moodtracker
```

## Docker 部署

### Dockerfile

```dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o bin/server cmd/server/main.go
RUN go build -o bin/migrate cmd/migrate/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/bin/server .
COPY --from=builder /app/bin/migrate .
COPY --from=builder /app/migrations ./migrations

EXPOSE 8080
CMD ["./server"]
```

### docker-compose.yml

```yaml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SERVER_PORT=8080
      - SERVER_GIN_MODE=release
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=moodtracker
      - DB_PASSWORD=password
      - DB_NAME=moodtracker
      - JWT_SECRET=your-super-secret-jwt-key
      - JWT_EXPIRES_IN=3600
      - LOG_LEVEL=info
    depends_on:
      - mysql
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=moodtracker
      - MYSQL_USER=moodtracker
      - MYSQL_PASSWORD=password
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    restart: unless-stopped

volumes:
  mysql_data:
```

### 部署命令

```bash
# 构建和启动
docker-compose up -d

# 运行数据库迁移
docker-compose exec api ./migrate

# 查看日志
docker-compose logs -f api

# 停止服务
docker-compose down
```

## 反向代理配置

### Nginx 配置

```nginx
server {
    listen 80;
    server_name api.moodtracker.com;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS headers
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Authorization, Content-Type";
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
}
```

## 监控和日志

### 日志配置

应用使用 logrus 进行结构化日志记录，支持以下日志级别：
- `debug`: 调试信息
- `info`: 一般信息
- `warn`: 警告信息
- `error`: 错误信息

### 健康检查

API 提供健康检查端点：
```bash
curl http://localhost:8080/health
```

### 监控指标

建议监控以下指标：
- HTTP 请求数量和响应时间
- 数据库连接状态
- 内存和CPU使用率
- 错误率和限流触发次数

## 安全配置

### 生产环境安全建议

1. **JWT密钥**: 使用强随机密钥，定期轮换
2. **数据库**: 使用专用用户，限制权限
3. **HTTPS**: 使用SSL/TLS加密传输
4. **防火墙**: 限制数据库端口访问
5. **日志**: 不记录敏感信息
6. **限流**: 根据实际需求调整限流参数

### CORS 配置

生产环境建议配置具体的允许域名：
```go
corsConfig := &middleware.CORSConfig{
    AllowOrigins: []string{
        "https://app.moodtracker.com",
        "https://admin.moodtracker.com",
    },
    AllowCredentials: true,
}
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否运行
   - 验证连接参数
   - 检查网络连接

2. **JWT验证失败**
   - 检查JWT密钥配置
   - 验证token格式
   - 检查token过期时间

3. **限流触发**
   - 检查请求频率
   - 调整限流参数
   - 检查客户端实现

### 日志分析

使用以下命令分析日志：
```bash
# 查看错误日志
journalctl -u moodtracker -p err

# 实时查看日志
journalctl -u moodtracker -f

# 查看特定时间段日志
journalctl -u moodtracker --since "2025-08-03 10:00:00"
```

## 备份和恢复

### 数据库备份

```bash
# 备份
mysqldump -u root -p moodtracker > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复
mysql -u root -p moodtracker < backup_20250803_120000.sql
```

### 自动备份脚本

```bash
#!/bin/bash
BACKUP_DIR="/opt/backups/moodtracker"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

mysqldump -u root -p$MYSQL_ROOT_PASSWORD moodtracker > $BACKUP_DIR/backup_$DATE.sql

# 保留最近7天的备份
find $BACKUP_DIR -name "backup_*.sql" -mtime +7 -delete
```
