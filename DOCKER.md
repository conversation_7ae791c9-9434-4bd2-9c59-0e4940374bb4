# MoodTracker API Docker 部署指南

## 快速开始

### 1. 修改配置

编辑 `docker-compose.yml` 文件中的数据库配置：

```yaml
environment:
  # 数据库配置 (连接你的MySQL，请修改这些值)
  - DB_HOST=localhost
  - DB_PORT=3306
  - DB_USERNAME=root
  - DB_PASSWORD=your_mysql_password  # 修改为你的MySQL密码
  - DB_NAME=moodtracker
  
  # JWT配置 (生产环境请修改密钥)
  - JWT_SECRET=your-super-secret-jwt-key-change-in-production  # 修改为强密钥
```

### 2. 快速部署

```bash
# 使用当前镜像部署
./scripts/quick-deploy.sh

# 使用指定标签部署
./scripts/quick-deploy.sh v1.0.0

# 使用latest标签部署
./scripts/quick-deploy.sh latest
```

### 3. 管理命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 进入容器
docker-compose exec api /bin/bash
```

## 手动部署

### 1. 构建镜像

```bash
# 构建本地镜像
./scripts/build.sh

# 构建并指定标签
./scripts/build.sh --tag v1.0.0

# 清理旧镜像后构建
./scripts/build.sh --clean
```

### 2. 推送镜像

```bash
# 构建镜像
docker build -t syferie/moodtracker:v1.0.0 .

# 推送到Docker Hub
docker push syferie/moodtracker:v1.0.0
```

### 3. 部署服务

```bash
# 启动服务
docker-compose up -d

# 运行数据库迁移
docker-compose exec api ./migrate
```

## 生产环境注意事项

### 1. 安全配置

- 修改 `JWT_SECRET` 为强随机密钥
- 使用专用的数据库用户，不要使用root
- 确保数据库密码足够强

### 2. 网络配置

如果API容器需要连接宿主机的MySQL：

```yaml
# 在docker-compose.yml中添加
network_mode: "host"
```

或者使用Docker网络：

```yaml
# 使用宿主机IP
- DB_HOST=host.docker.internal  # macOS/Windows
- DB_HOST=**********           # Linux
```

### 3. 数据持久化

```yaml
# 添加日志卷挂载
volumes:
  - ./logs:/app/logs
  - ./data:/app/data  # 如果需要持久化其他数据
```

### 4. 资源限制

```yaml
# 添加资源限制
deploy:
  resources:
    limits:
      cpus: '1.0'
      memory: 512M
    reservations:
      cpus: '0.5'
      memory: 256M
```

## 故障排除

### 1. 数据库连接失败

```bash
# 检查数据库连接
docker-compose exec api ./migrate

# 查看详细日志
docker-compose logs api
```

### 2. 端口冲突

```bash
# 修改端口映射
ports:
  - "8081:8080"  # 使用8081端口
```

### 3. 权限问题

```bash
# 检查文件权限
ls -la logs/

# 修复权限
sudo chown -R $USER:$USER logs/
```

## 监控和维护

### 1. 健康检查

```bash
# 检查API健康状态
curl http://localhost:8080/health

# 检查容器健康状态
docker-compose ps
```

### 2. 日志管理

```bash
# 查看实时日志
docker-compose logs -f api

# 查看最近的日志
docker-compose logs --tail=100 api

# 清理日志
docker-compose down && docker system prune -f
```

### 3. 更新部署

```bash
# 拉取新镜像并重新部署
docker-compose pull
docker-compose up -d

# 或使用快速部署脚本
./scripts/quick-deploy.sh latest
```
