package main

import (
	"log"

	"github.com/sirupsen/logrus"
	"moodtracker-api/config"
	"moodtracker-api/internal/database"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// 连接数据库
	db, err := database.NewConnection(&cfg.Database)
	if err != nil {
		logrus.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// 检查表是否存在
	tables := []string{"users", "couples", "moods", "refresh_tokens", "migrations"}
	
	for _, table := range tables {
		var count int
		query := "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ? AND table_name = ?"
		err := db.QueryRow(query, cfg.Database.Name, table).Scan(&count)
		if err != nil {
			logrus.Errorf("Failed to check table %s: %v", table, err)
			continue
		}
		
		if count > 0 {
			logrus.Infof("✅ Table '%s' exists", table)
		} else {
			logrus.Errorf("❌ Table '%s' does not exist", table)
		}
	}

	// 检查表结构
	logrus.Info("Checking table structures...")
	
	// 检查users表结构
	rows, err := db.Query("DESCRIBE users")
	if err != nil {
		logrus.Error("Failed to describe users table:", err)
	} else {
		logrus.Info("Users table structure:")
		for rows.Next() {
			var field, fieldType, null, key, defaultVal, extra string
			rows.Scan(&field, &fieldType, &null, &key, &defaultVal, &extra)
			logrus.Infof("  %s: %s", field, fieldType)
		}
		rows.Close()
	}

	logrus.Info("Database verification completed")
}
