package main

import (
	"fmt"
	"log"

	"moodtracker-api/config"
	"moodtracker-api/internal/database"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := database.NewConnection(&cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("🚨 开始彻底清理数据库中的所有表...")
	fmt.Println()

	// 禁用外键检查
	if _, err := db.Exec("SET FOREIGN_KEY_CHECKS = 0"); err != nil {
		log.Printf("Warning: Failed to disable foreign key checks: %v", err)
	}

	// 禁用事件调度器
	if _, err := db.Exec("SET GLOBAL event_scheduler = OFF"); err != nil {
		log.Printf("Warning: Failed to disable event scheduler: %v", err)
	}

	// 删除所有事件
	events := []string{"cleanup_expired_data"}
	for _, event := range events {
		if _, err := db.Exec("DROP EVENT IF EXISTS " + event); err != nil {
			log.Printf("Warning: Failed to drop event %s: %v", event, err)
		} else {
			fmt.Printf("✅ 删除事件: %s\n", event)
		}
	}

	// 删除所有触发器
	triggers := []string{"update_last_login", "couple_matched"}
	for _, trigger := range triggers {
		if _, err := db.Exec("DROP TRIGGER IF EXISTS " + trigger); err != nil {
			log.Printf("Warning: Failed to drop trigger %s: %v", trigger, err)
		} else {
			fmt.Printf("✅ 删除触发器: %s\n", trigger)
		}
	}

	// 删除所有视图
	views := []string{"active_couples", "user_mood_stats"}
	for _, view := range views {
		if _, err := db.Exec("DROP VIEW IF EXISTS " + view); err != nil {
			log.Printf("Warning: Failed to drop view %s: %v", view, err)
		} else {
			fmt.Printf("✅ 删除视图: %s\n", view)
		}
	}

	// 删除所有存储过程和函数
	procedures := []string{"CleanupExpiredData"}
	functions := []string{"GetUserStats"}
	
	for _, proc := range procedures {
		if _, err := db.Exec("DROP PROCEDURE IF EXISTS " + proc); err != nil {
			log.Printf("Warning: Failed to drop procedure %s: %v", proc, err)
		} else {
			fmt.Printf("✅ 删除存储过程: %s\n", proc)
		}
	}
	
	for _, fn := range functions {
		if _, err := db.Exec("DROP FUNCTION IF EXISTS " + fn); err != nil {
			log.Printf("Warning: Failed to drop function %s: %v", fn, err)
		} else {
			fmt.Printf("✅ 删除函数: %s\n", fn)
		}
	}

	// 删除所有表（包括你看到的所有冗余表）
	tables := []string{
		"sync_logs",
		"user_auth", 
		"user_devices",
		"mood_entries",
		"moods",
		"refresh_tokens",
		"couples",
		"users",
		"migrations",
	}

	fmt.Println()
	fmt.Println("🗑️  删除所有表:")
	for _, table := range tables {
		if _, err := db.Exec("DROP TABLE IF EXISTS " + table); err != nil {
			log.Printf("Warning: Failed to drop table %s: %v", table, err)
		} else {
			fmt.Printf("✅ 删除表: %s\n", table)
		}
	}

	// 重新启用外键检查
	if _, err := db.Exec("SET FOREIGN_KEY_CHECKS = 1"); err != nil {
		log.Printf("Warning: Failed to enable foreign key checks: %v", err)
	}

	fmt.Println()
	fmt.Println("📋 检查剩余的表:")
	
	// 查询剩余的表
	rows, err := db.Query("SHOW TABLES")
	if err != nil {
		log.Printf("Failed to show tables: %v", err)
	} else {
		defer rows.Close()
		tableCount := 0
		for rows.Next() {
			var tableName string
			if err := rows.Scan(&tableName); err != nil {
				continue
			}
			fmt.Printf("   - %s\n", tableName)
			tableCount++
		}
		
		if tableCount == 0 {
			fmt.Println("   🎉 没有剩余的表！数据库完全清理干净！")
		} else {
			fmt.Printf("   ⚠️  还有 %d 个表剩余\n", tableCount)
		}
	}

	fmt.Println()
	fmt.Println("🎯 清理完成!")
	fmt.Println("✅ 数据库保留")
	fmt.Println("✅ 所有表已删除")
	fmt.Println("✅ 所有数据已清理")
	fmt.Println("✅ 准备重新初始化")
}
