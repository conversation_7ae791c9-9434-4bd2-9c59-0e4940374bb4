package main

import (
	"log"

	"moodtracker-api/config"
	"moodtracker-api/internal/database"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := database.NewConnection(&cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	log.Println("Cleaning database...")

	// 禁用外键检查
	if _, err := db.Exec("SET FOREIGN_KEY_CHECKS = 0"); err != nil {
		log.Fatalf("Failed to disable foreign key checks: %v", err)
	}

	// 清理所有数据（按依赖关系顺序）
	tables := []string{"moods", "refresh_tokens", "couples", "users"}
	
	for _, table := range tables {
		if _, err := db.Exec("DELETE FROM " + table); err != nil {
			log.Printf("Warning: Failed to clean table %s: %v", table, err)
		} else {
			log.Printf("Cleaned table: %s", table)
		}
	}

	// 启用外键检查
	if _, err := db.Exec("SET FOREIGN_KEY_CHECKS = 1"); err != nil {
		log.Fatalf("Failed to enable foreign key checks: %v", err)
	}

	// 验证清理结果
	for _, table := range tables {
		var count int
		if err := db.QueryRow("SELECT COUNT(*) FROM " + table).Scan(&count); err != nil {
			log.Printf("Warning: Failed to count table %s: %v", table, err)
		} else {
			log.Printf("Table %s: %d records", table, count)
		}
	}

	log.Println("Database cleanup completed!")
}
