package main

import (
	"flag"
	"log"
	"path/filepath"

	"github.com/sirupsen/logrus"
	"moodtracker-api/config"
	"moodtracker-api/internal/database"
)

func main() {
	var migrationsDir string
	flag.StringVar(&migrationsDir, "dir", "migrations", "Directory containing migration files")
	flag.Parse()

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.Log.Level)
	if err != nil {
		logrus.Warn("Invalid log level, using info")
		level = logrus.InfoLevel
	}
	logrus.SetLevel(level)

	// 连接数据库
	db, err := database.NewConnection(&cfg.Database)
	if err != nil {
		logrus.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// 创建迁移器
	migrator := database.NewMigrator(db)

	// 获取迁移文件目录的绝对路径
	absDir, err := filepath.Abs(migrationsDir)
	if err != nil {
		logrus.Fatal("Failed to get absolute path for migrations directory:", err)
	}

	logrus.Infof("Running migrations from directory: %s", absDir)

	// 执行迁移
	if err := migrator.Migrate(absDir); err != nil {
		logrus.Fatal("Migration failed:", err)
	}

	logrus.Info("Migration completed successfully")
}
