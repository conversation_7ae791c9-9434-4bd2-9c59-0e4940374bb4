package main

import (
	"fmt"
	"log"

	"moodtracker-api/config"
	"moodtracker-api/internal/database"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := database.NewConnection(&cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("🔧 强制创建所有必需的表...")
	fmt.Println()

	// 直接创建用户表
	fmt.Println("📋 创建 users 表...")
	userSQL := `
	CREATE TABLE IF NOT EXISTS users (
		user_id VARCHAR(36) PRIMARY KEY,
		username VARCHAR(50) UNIQUE NOT NULL,
		password_hash VARCHAR(255) NOT NULL,
		nickname VARCHAR(100),
		avatar_url VARCHAR(500),
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		last_login_at TIMESTAMP NULL,
		INDEX idx_username (username)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`
	
	if _, err := db.Exec(userSQL); err != nil {
		log.Printf("❌ 创建 users 表失败: %v", err)
	} else {
		fmt.Println("✅ users 表创建成功")
	}

	// 创建情侣关系表
	fmt.Println("📋 创建 couples 表...")
	coupleSQL := `
	CREATE TABLE IF NOT EXISTS couples (
		couple_id VARCHAR(36) PRIMARY KEY,
		user1_id VARCHAR(36) NOT NULL,
		user2_id VARCHAR(36),
		match_code VARCHAR(8),
		relationship_name VARCHAR(100),
		status ENUM('waiting', 'matched') DEFAULT 'waiting',
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		matched_at TIMESTAMP NULL,
		expires_at TIMESTAMP NOT NULL DEFAULT (CURRENT_TIMESTAMP + INTERVAL 1 HOUR),
		FOREIGN KEY (user1_id) REFERENCES users(user_id) ON DELETE CASCADE,
		FOREIGN KEY (user2_id) REFERENCES users(user_id) ON DELETE CASCADE,
		INDEX idx_match_code (match_code),
		INDEX idx_user1 (user1_id),
		INDEX idx_user2 (user2_id)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`
	
	if _, err := db.Exec(coupleSQL); err != nil {
		log.Printf("❌ 创建 couples 表失败: %v", err)
	} else {
		fmt.Println("✅ couples 表创建成功")
	}

	// 创建心情记录表
	fmt.Println("📋 创建 moods 表...")
	moodSQL := `
	CREATE TABLE IF NOT EXISTS moods (
		entry_id VARCHAR(36) PRIMARY KEY,
		user_id VARCHAR(36) NOT NULL,
		couple_id VARCHAR(36) NOT NULL,
		date DATE NOT NULL,
		mood TINYINT NOT NULL CHECK (mood >= 1 AND mood <= 12),
		note TEXT,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		sync_status ENUM('PENDING', 'SYNCED', 'CONFLICT') DEFAULT 'SYNCED',
		is_deleted BOOLEAN DEFAULT FALSE,
		FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
		FOREIGN KEY (couple_id) REFERENCES couples(couple_id) ON DELETE CASCADE,
		UNIQUE KEY unique_user_date (user_id, date),
		INDEX idx_couple_date (couple_id, date),
		INDEX idx_updated_at (updated_at),
		INDEX idx_sync_status (sync_status)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`
	
	if _, err := db.Exec(moodSQL); err != nil {
		log.Printf("❌ 创建 moods 表失败: %v", err)
	} else {
		fmt.Println("✅ moods 表创建成功")
	}

	// 创建刷新令牌表
	fmt.Println("📋 创建 refresh_tokens 表...")
	tokenSQL := `
	CREATE TABLE IF NOT EXISTS refresh_tokens (
		token_id VARCHAR(36) PRIMARY KEY,
		user_id VARCHAR(36) NOT NULL,
		token_hash VARCHAR(255) NOT NULL,
		device_id VARCHAR(36),
		expires_at TIMESTAMP NOT NULL,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
		INDEX idx_user_device (user_id, device_id),
		INDEX idx_expires_at (expires_at)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`
	
	if _, err := db.Exec(tokenSQL); err != nil {
		log.Printf("❌ 创建 refresh_tokens 表失败: %v", err)
	} else {
		fmt.Println("✅ refresh_tokens 表创建成功")
	}

	fmt.Println()
	fmt.Println("🔍 验证所有表:")
	
	// 检查表是否创建成功
	tables := []string{"users", "couples", "moods", "refresh_tokens"}
	allSuccess := true
	
	for _, table := range tables {
		var exists bool
		query := "SELECT COUNT(*) > 0 FROM information_schema.tables WHERE table_schema = ? AND table_name = ?"
		err := db.QueryRow(query, cfg.Database.Name, table).Scan(&exists)
		if err != nil {
			fmt.Printf("   ❌ %s: 检查失败 - %v\n", table, err)
			allSuccess = false
		} else if exists {
			// 检查记录数
			var count int
			if err := db.QueryRow("SELECT COUNT(*) FROM " + table).Scan(&count); err != nil {
				fmt.Printf("   ✅ %s: 表存在 (记录数查询失败)\n", table)
			} else {
				fmt.Printf("   ✅ %s: 表存在，%d 条记录\n", table, count)
			}
		} else {
			fmt.Printf("   ❌ %s: 表不存在\n", table)
			allSuccess = false
		}
	}

	fmt.Println()
	if allSuccess {
		fmt.Println("🎉 所有表创建成功！数据库初始化完成！")
	} else {
		fmt.Println("⚠️  部分表创建失败，请检查错误信息")
	}
}
