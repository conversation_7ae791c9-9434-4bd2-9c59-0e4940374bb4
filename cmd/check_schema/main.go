package main

import (
	"fmt"
	"log"
	"strings"

	"moodtracker-api/config"
	"moodtracker-api/internal/database"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := database.NewConnection(&cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("🔍 检查 couples 表结构:")
	fmt.Println()

	// 查询表结构
	rows, err := db.Query("DESCRIBE couples")
	if err != nil {
		log.Fatalf("Failed to describe couples table: %v", err)
	}
	defer rows.Close()

	fmt.Printf("%-20s %-15s %-10s %-10s %-10s %-10s\n", "Field", "Type", "Null", "Key", "Default", "Extra")
	fmt.Println(strings.Repeat("-", 80))

	for rows.Next() {
		var field, fieldType, null, key, defaultVal, extra string
		var defaultPtr *string

		if err := rows.Scan(&field, &fieldType, &null, &key, &defaultPtr, &extra); err != nil {
			log.Printf("Error scanning row: %v", err)
			continue
		}

		if defaultPtr != nil {
			defaultVal = *defaultPtr
		} else {
			defaultVal = "NULL"
		}

		fmt.Printf("%-20s %-15s %-10s %-10s %-10s %-10s\n", field, fieldType, null, key, defaultVal, extra)
	}

	fmt.Println()
	fmt.Println("🔍 检查代码中使用的字段:")

	// 这里我们需要检查代码中使用的字段名
	expectedFields := []string{
		"couple_id",
		"user1_id",
		"user2_id",
		"match_code",
		"relationship_name",
		"status",
		"created_at",
		"matched_at",
		"expires_at", // 这个字段可能有问题
	}

	for _, field := range expectedFields {
		var exists bool
		query := "SELECT COUNT(*) > 0 FROM information_schema.columns WHERE table_schema = ? AND table_name = 'couples' AND column_name = ?"
		err := db.QueryRow(query, cfg.Database.Name, field).Scan(&exists)
		if err != nil {
			fmt.Printf("   ❌ %s: 检查失败 - %v\n", field, err)
		} else if exists {
			fmt.Printf("   ✅ %s: 字段存在\n", field)
		} else {
			fmt.Printf("   ❌ %s: 字段不存在\n", field)
		}
	}
}
