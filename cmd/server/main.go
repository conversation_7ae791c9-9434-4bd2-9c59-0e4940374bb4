package main

import (
	"log"

	"moodtracker-api/config"
	"moodtracker-api/internal/database"
	"moodtracker-api/internal/handlers"
	"moodtracker-api/internal/middleware"
	"moodtracker-api/internal/services"
	"moodtracker-api/pkg/response"
	"moodtracker-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.Log.Level)
	if err != nil {
		logrus.Warn("Invalid log level, using info")
		level = logrus.InfoLevel
	}
	logrus.SetLevel(level)

	// 连接数据库
	db, err := database.NewConnection(&cfg.Database)
	if err != nil {
		logrus.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// 创建JWT管理器
	jwtManager := utils.NewJWTManager(cfg.JWT.Secret, cfg.JWT.ExpiresIn)

	// 创建服务
	userService := services.NewUserService(db)
	authService := services.NewAuthService(db, jwtManager, userService)
	coupleService := services.NewCoupleService(db, userService)
	moodService := services.NewMoodService(db, coupleService)
	syncService := services.NewSyncService(db, coupleService, moodService)

	// 创建处理器
	authHandler := handlers.NewAuthHandler(authService)
	coupleHandler := handlers.NewCoupleHandler(coupleService)
	moodHandler := handlers.NewMoodHandler(moodService)
	syncHandler := handlers.NewSyncHandler(syncService)

	// 设置Gin模式
	gin.SetMode(cfg.Server.GinMode)

	// 创建Gin实例（不使用默认中间件）
	r := gin.New()

	// 添加中间件
	r.Use(middleware.RecoveryMiddleware())
	r.Use(middleware.LoggingMiddleware())
	r.Use(middleware.CORSMiddleware(middleware.DefaultCORSConfig()))
	r.Use(middleware.ErrorHandlerMiddleware())

	// 创建限流器
	rateLimiter := middleware.NewInMemoryRateLimiter(middleware.DefaultRateLimitConfig())
	rateLimiter.CleanupExpiredBuckets()
	r.Use(middleware.RateLimitMiddleware(rateLimiter, middleware.DefaultRateLimitConfig()))

	// 基础路由
	r.GET("/health", func(c *gin.Context) {
		if err := db.HealthCheck(); err != nil {
			response.InternalServerError(c, "Database connection failed")
			return
		}
		response.Success(c, map[string]string{
			"status":   "ok",
			"database": "connected",
		}, "服务运行正常")
	})

	// 测试panic恢复的端点（仅用于测试）
	r.GET("/test-panic", func(c *gin.Context) {
		panic("This is a test panic for recovery middleware")
	})

	// API路由组
	api := r.Group("/api/v1")
	{
		// 认证路由
		auth := api.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
			auth.POST("/refresh-token", authHandler.RefreshToken)
			auth.POST("/logout", authHandler.Logout)
		}

		// 需要认证的路由
		protected := api.Group("")
		protected.Use(middleware.AuthMiddleware(authService))
		{
			protected.GET("/user/me", authHandler.GetCurrentUser)
		}

		// 情侣路由（需要认证）
		couples := api.Group("/couples")
		couples.Use(middleware.AuthMiddleware(authService))
		{
			couples.POST("/create", coupleHandler.CreateCouple)
			couples.POST("/join", coupleHandler.JoinCouple)
			couples.GET("/info", coupleHandler.GetCoupleInfo)
			couples.DELETE("/leave", coupleHandler.LeaveCouple)
		}

		// 心情路由（需要认证）
		moods := api.Group("/moods")
		moods.Use(middleware.AuthMiddleware(authService))
		{
			moods.POST("", moodHandler.CreateMood)
			moods.PUT("/:entryId", moodHandler.UpdateMood)
			moods.DELETE("/:entryId", moodHandler.DeleteMood)
			moods.GET("/couple", moodHandler.GetCoupleMoods)
		}

		// 同步路由（需要认证）
		sync := api.Group("/sync")
		sync.Use(middleware.AuthMiddleware(authService))
		{
			sync.GET("/changes", syncHandler.GetChanges)
			sync.POST("/upload", syncHandler.UploadChanges)
			sync.GET("/last-sync-time", syncHandler.GetLastSyncTime)
		}
	}

	// 启动服务器
	logrus.Infof("Server starting on port %s", cfg.Server.Port)
	if err := r.Run(":" + cfg.Server.Port); err != nil {
		logrus.Fatal("Failed to start server:", err)
	}
}
