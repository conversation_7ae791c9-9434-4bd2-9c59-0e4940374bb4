package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"strings"

	"moodtracker-api/config"
	"moodtracker-api/internal/database"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := database.NewConnection(&cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("🔧 手动执行数据库迁移...")
	fmt.Println()

	// 读取迁移文件
	migrationFile := "migrations/001_create_tables.sql"
	content, err := ioutil.ReadFile(migrationFile)
	if err != nil {
		log.Fatalf("Failed to read migration file: %v", err)
	}

	// 分割SQL语句
	statements := strings.Split(string(content), ";")
	
	fmt.Printf("📄 读取迁移文件: %s\n", migrationFile)
	fmt.Printf("📊 发现 %d 个SQL语句\n", len(statements))
	fmt.Println()

	// 执行每个SQL语句
	for i, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" || strings.HasPrefix(stmt, "--") {
			continue
		}

		fmt.Printf("⚡ 执行语句 %d: %s...\n", i+1, stmt[:min(50, len(stmt))])
		
		if _, err := db.Exec(stmt); err != nil {
			log.Printf("❌ 语句执行失败: %v", err)
			log.Printf("   语句内容: %s", stmt)
		} else {
			fmt.Printf("✅ 语句执行成功\n")
		}
	}

	fmt.Println()
	fmt.Println("🔍 验证表创建结果:")
	
	// 检查表是否创建成功
	tables := []string{"users", "couples", "moods", "refresh_tokens"}
	for _, table := range tables {
		var exists bool
		query := "SELECT COUNT(*) > 0 FROM information_schema.tables WHERE table_schema = ? AND table_name = ?"
		err := db.QueryRow(query, cfg.Database.Name, table).Scan(&exists)
		if err != nil {
			fmt.Printf("   ❌ %s: 检查失败 - %v\n", table, err)
		} else if exists {
			fmt.Printf("   ✅ %s: 表创建成功\n", table)
		} else {
			fmt.Printf("   ❌ %s: 表不存在\n", table)
		}
	}

	fmt.Println()
	fmt.Println("🎯 手动迁移完成!")
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
