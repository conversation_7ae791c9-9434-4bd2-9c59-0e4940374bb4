package response

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// APIResponse 通用API响应结构
type APIResponse struct {
	Success   bool        `json:"success"`
	Message   *string     `json:"message"`
	Data      interface{} `json:"data"`
	Error     *string     `json:"error"`
	Timestamp string      `json:"timestamp"`
}

// Success 成功响应
func Success(c *gin.Context, data interface{}, message ...string) {
	var msg *string
	if len(message) > 0 {
		msg = &message[0]
	}

	response := APIResponse{
		Success:   true,
		Message:   msg,
		Data:      data,
		Error:     nil,
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}

	c.JSO<PERSON>(http.StatusOK, response)
}

// Error 错误响应
func Error(c *gin.Context, statusCode int, errorMsg string) {
	response := APIResponse{
		Success:   false,
		Message:   nil,
		Data:      nil,
		Error:     &errorMsg,
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}

	c.J<PERSON>N(statusCode, response)
}

// BadRequest 400错误
func BadRequest(c *gin.Context, errorMsg string) {
	Error(c, http.StatusBadRequest, errorMsg)
}

// Unauthorized 401错误
func Unauthorized(c *gin.Context, errorMsg string) {
	Error(c, http.StatusUnauthorized, errorMsg)
}

// Forbidden 403错误
func Forbidden(c *gin.Context, errorMsg string) {
	Error(c, http.StatusForbidden, errorMsg)
}

// NotFound 404错误
func NotFound(c *gin.Context, errorMsg string) {
	Error(c, http.StatusNotFound, errorMsg)
}

// InternalServerError 500错误
func InternalServerError(c *gin.Context, errorMsg string) {
	Error(c, http.StatusInternalServerError, errorMsg)
}
