package utils

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"moodtracker-api/internal/models"
)

// JWTManager JWT管理器
type JWTManager struct {
	secretKey string
	expiresIn int
}

// NewJWTManager 创建JWT管理器
func NewJWTManager(secretKey string, expiresIn int) *JWTManager {
	return &JWTManager{
		secretKey: secretKey,
		expiresIn: expiresIn,
	}
}

// GenerateToken 生成JWT token
func (j *JWTManager) GenerateToken(userID, username, deviceID string) (string, time.Time, error) {
	now := time.Now()
	expiresAt := now.Add(time.Duration(j.expiresIn) * time.Second)

	claims := &models.JWTClaims{
		UserID:    userID,
		Username:  username,
		DeviceID:  deviceID,
		IssuedAt:  now.Unix(),
		ExpiresAt: expiresAt.Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"sub":       claims.UserID,
		"username":  claims.Username,
		"device_id": claims.DeviceID,
		"iat":       claims.IssuedAt,
		"exp":       claims.ExpiresAt,
	})

	tokenString, err := token.SignedString([]byte(j.secretKey))
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, expiresAt, nil
}

// ValidateToken 验证JWT token
func (j *JWTManager) ValidateToken(tokenString string) (*models.JWTClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(j.secretKey), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}

	// 检查过期时间
	if exp, ok := claims["exp"].(float64); ok {
		if time.Now().Unix() > int64(exp) {
			return nil, fmt.Errorf("token expired")
		}
	}

	// 提取声明
	jwtClaims := &models.JWTClaims{}
	
	if sub, ok := claims["sub"].(string); ok {
		jwtClaims.UserID = sub
	}
	
	if username, ok := claims["username"].(string); ok {
		jwtClaims.Username = username
	}
	
	if deviceID, ok := claims["device_id"].(string); ok {
		jwtClaims.DeviceID = deviceID
	}
	
	if iat, ok := claims["iat"].(float64); ok {
		jwtClaims.IssuedAt = int64(iat)
	}
	
	if exp, ok := claims["exp"].(float64); ok {
		jwtClaims.ExpiresAt = int64(exp)
	}

	return jwtClaims, nil
}

// GenerateRefreshToken 生成刷新令牌
func (j *JWTManager) GenerateRefreshToken() (string, error) {
	return GenerateRandomString(32)
}

// GetTokenExpirationTime 获取token过期时间
func (j *JWTManager) GetTokenExpirationTime() time.Duration {
	return time.Duration(j.expiresIn) * time.Second
}
