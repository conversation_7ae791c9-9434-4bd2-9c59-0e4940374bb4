package utils

import (
	"testing"
	"time"
)

func TestJWTManager_GenerateToken(t *testing.T) {
	secretKey := "test-secret-key"
	expiresIn := 3600 // 1 hour
	
	jwtManager := NewJWTManager(secretKey, expiresIn)
	
	userID := "test-user-id"
	username := "testuser"
	deviceID := "test-device-id"
	
	token, expiresAt, err := jwtManager.GenerateToken(userID, username, deviceID)
	if err != nil {
		t.Fatalf("GenerateToken failed: %v", err)
	}
	
	if token == "" {
		t.Error("Token should not be empty")
	}
	
	if expiresAt.IsZero() {
		t.Error("ExpiresAt should not be zero")
	}
	
	// Check if expires at is approximately 1 hour from now
	expectedExpiry := time.Now().Add(time.Duration(expiresIn) * time.Second)
	if expiresAt.Sub(expectedExpiry) > time.Minute {
		t.Error("ExpiresAt time is not correct")
	}
}

func TestJWTManager_ValidateToken(t *testing.T) {
	secretKey := "test-secret-key"
	expiresIn := 3600
	
	jwtManager := NewJWTManager(secretKey, expiresIn)
	
	userID := "test-user-id"
	username := "testuser"
	deviceID := "test-device-id"
	
	// Generate token
	token, _, err := jwtManager.GenerateToken(userID, username, deviceID)
	if err != nil {
		t.Fatalf("GenerateToken failed: %v", err)
	}
	
	// Validate token
	claims, err := jwtManager.ValidateToken(token)
	if err != nil {
		t.Fatalf("ValidateToken failed: %v", err)
	}
	
	if claims.UserID != userID {
		t.Errorf("Expected UserID %s, got %s", userID, claims.UserID)
	}
	
	if claims.Username != username {
		t.Errorf("Expected Username %s, got %s", username, claims.Username)
	}
	
	if claims.DeviceID != deviceID {
		t.Errorf("Expected DeviceID %s, got %s", deviceID, claims.DeviceID)
	}
}

func TestJWTManager_ValidateToken_InvalidToken(t *testing.T) {
	secretKey := "test-secret-key"
	expiresIn := 3600
	
	jwtManager := NewJWTManager(secretKey, expiresIn)
	
	// Test with invalid token
	_, err := jwtManager.ValidateToken("invalid-token")
	if err == nil {
		t.Error("ValidateToken should fail with invalid token")
	}
}

func TestJWTManager_ValidateToken_WrongSecret(t *testing.T) {
	secretKey1 := "test-secret-key-1"
	secretKey2 := "test-secret-key-2"
	expiresIn := 3600
	
	jwtManager1 := NewJWTManager(secretKey1, expiresIn)
	jwtManager2 := NewJWTManager(secretKey2, expiresIn)
	
	userID := "test-user-id"
	username := "testuser"
	deviceID := "test-device-id"
	
	// Generate token with first manager
	token, _, err := jwtManager1.GenerateToken(userID, username, deviceID)
	if err != nil {
		t.Fatalf("GenerateToken failed: %v", err)
	}
	
	// Try to validate with second manager (different secret)
	_, err = jwtManager2.ValidateToken(token)
	if err == nil {
		t.Error("ValidateToken should fail with wrong secret")
	}
}

func TestJWTManager_ValidateToken_ExpiredToken(t *testing.T) {
	secretKey := "test-secret-key"
	expiresIn := 1 // 1 second
	
	jwtManager := NewJWTManager(secretKey, expiresIn)
	
	userID := "test-user-id"
	username := "testuser"
	deviceID := "test-device-id"
	
	// Generate token
	token, _, err := jwtManager.GenerateToken(userID, username, deviceID)
	if err != nil {
		t.Fatalf("GenerateToken failed: %v", err)
	}
	
	// Wait for token to expire
	time.Sleep(2 * time.Second)
	
	// Try to validate expired token
	_, err = jwtManager.ValidateToken(token)
	if err == nil {
		t.Error("ValidateToken should fail with expired token")
	}
}

func TestJWTManager_GenerateRefreshToken(t *testing.T) {
	secretKey := "test-secret-key"
	expiresIn := 3600
	
	jwtManager := NewJWTManager(secretKey, expiresIn)
	
	token1, err := jwtManager.GenerateRefreshToken()
	if err != nil {
		t.Fatalf("GenerateRefreshToken failed: %v", err)
	}
	
	token2, err := jwtManager.GenerateRefreshToken()
	if err != nil {
		t.Fatalf("GenerateRefreshToken failed: %v", err)
	}
	
	if token1 == "" {
		t.Error("Refresh token should not be empty")
	}
	
	if token1 == token2 {
		t.Error("Two refresh tokens should not be equal")
	}
	
	if len(token1) != 32 {
		t.Errorf("Expected refresh token length 32, got %d", len(token1))
	}
}

func TestJWTManager_GetTokenExpirationTime(t *testing.T) {
	secretKey := "test-secret-key"
	expiresIn := 3600
	
	jwtManager := NewJWTManager(secretKey, expiresIn)
	
	duration := jwtManager.GetTokenExpirationTime()
	expected := time.Duration(expiresIn) * time.Second
	
	if duration != expected {
		t.Errorf("Expected duration %v, got %v", expected, duration)
	}
}
