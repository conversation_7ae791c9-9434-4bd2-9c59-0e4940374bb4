package utils

import (
	"testing"
)

func TestHashPassword(t *testing.T) {
	password := "testpassword123"

	hash, err := HashPassword(password)
	if err != nil {
		t.Fatalf("HashPassword failed: %v", err)
	}

	if hash == "" {
		t.<PERSON>rror("Hash should not be empty")
	}

	if hash == password {
		t.<PERSON>rror("Hash should not equal original password")
	}
}

func TestCheckPassword(t *testing.T) {
	password := "testpassword123"
	wrongPassword := "wrongpassword"

	hash, err := HashPassword(password)
	if err != nil {
		t.Fatalf("HashPassword failed: %v", err)
	}

	// Test correct password
	if !CheckPassword(password, hash) {
		t.<PERSON>r("CheckPassword should return true for correct password")
	}

	// Test wrong password
	if CheckPassword(wrongPassword, hash) {
		t.Error("CheckPassword should return false for wrong password")
	}
}

func TestGenerateRandomString(t *testing.T) {
	length := 32

	str1, err := GenerateRandomString(length)
	if err != nil {
		t.Fatalf("GenerateRandomString failed: %v", err)
	}

	str2, err := GenerateRandomString(length)
	if err != nil {
		t.Fatalf("GenerateRandomString failed: %v", err)
	}

	if len(str1) != length {
		t.Errorf("Expected length %d, got %d", length, len(str1))
	}

	if str1 == str2 {
		t.Error("Two random strings should not be equal")
	}
}

func TestGenerateMatchCode(t *testing.T) {
	code1, err := GenerateMatchCode()
	if err != nil {
		t.Fatalf("GenerateMatchCode failed: %v", err)
	}

	code2, err := GenerateMatchCode()
	if err != nil {
		t.Fatalf("GenerateMatchCode failed: %v", err)
	}

	if len(code1) != 8 {
		t.Errorf("Expected length 8, got %d", len(code1))
	}

	if len(code2) != 8 {
		t.Errorf("Expected length 8, got %d", len(code2))
	}

	if code1 == code2 {
		t.Error("Two match codes should not be equal")
	}

	// Check if all characters are digits
	for _, char := range code1 {
		if char < '0' || char > '9' {
			t.Errorf("Match code should only contain digits, found: %c", char)
		}
	}
}

func TestHashToken(t *testing.T) {
	token := "test-token-123"

	hash1 := HashToken(token)
	hash2 := HashToken(token)

	if hash1 != hash2 {
		t.Error("Same token should produce same hash")
	}

	if hash1 == token {
		t.Error("Hash should not equal original token")
	}

	if len(hash1) != 64 { // SHA256 produces 64 character hex string
		t.Errorf("Expected hash length 64, got %d", len(hash1))
	}
}

func TestValidateUsername(t *testing.T) {
	tests := []struct {
		username string
		expected bool
	}{
		{"validuser", true},
		{"user123", true},
		{"user_name", true},
		{"User123", true},
		{"ab", false},        // too short
		{"a", false},         // too short
		{"", false},          // empty
		{"user@name", false}, // invalid character
		{"user name", false}, // space not allowed
		{"user-name", false}, // dash not allowed
		{"verylongusernamethatexceedstwentycharacters", false}, // too long
	}

	for _, test := range tests {
		result := ValidateUsername(test.username)
		if result != test.expected {
			t.Errorf("ValidateUsername(%q) = %v, expected %v", test.username, result, test.expected)
		}
	}
}

func TestValidatePassword(t *testing.T) {
	tests := []struct {
		password string
		expected bool
	}{
		{"password123", true},
		{"123456", true},
		{"verylongpasswordthatismorethanfiftycharacterslongandexceedsthelimit", false}, // too long
		{"12345", false}, // too short
		{"", false},      // empty
	}

	for _, test := range tests {
		result := ValidatePassword(test.password)
		if result != test.expected {
			t.Errorf("ValidatePassword(%q) = %v, expected %v", test.password, result, test.expected)
		}
	}
}
