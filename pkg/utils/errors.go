package utils

// 错误代码常量
const (
	// 通用错误
	ErrInternalServer = "INTERNAL_SERVER_ERROR"
	ErrInvalidRequest = "INVALID_REQUEST"
	ErrUnauthorized   = "UNAUTHORIZED"
	ErrForbidden      = "FORBIDDEN"
	ErrNotFound       = "NOT_FOUND"

	// 认证错误
	ErrInvalidCredentials = "INVALID_CREDENTIALS"
	ErrUserExists         = "USER_ALREADY_EXISTS"
	ErrTokenExpired       = "TOKEN_EXPIRED"
	ErrTokenInvalid       = "TOKEN_INVALID"

	// 业务错误
	ErrCoupleExists     = "COUPLE_ALREADY_EXISTS"
	ErrInvalidMatchCode = "INVALID_MATCH_CODE"
	ErrMatchCodeExpired = "MATCH_CODE_EXPIRED"
	ErrMoodExists       = "MOOD_ALREADY_EXISTS"
	ErrInvalidMoodValue = "INVALID_MOOD_VALUE"
)

// 错误消息映射
var ErrorMessages = map[string]string{
	ErrInternalServer:     "内部服务器错误",
	ErrInvalidRequest:     "请求参数无效",
	ErrUnauthorized:       "未授权访问",
	ErrForbidden:          "禁止访问",
	ErrNotFound:           "资源不存在",
	ErrInvalidCredentials: "用户名或密码错误",
	ErrUserExists:         "用户已存在",
	ErrTokenExpired:       "Token已过期",
	ErrTokenInvalid:       "Token无效",
	ErrCoupleExists:       "情侣关系已存在",
	ErrInvalidMatchCode:   "匹配码无效",
	ErrMatchCodeExpired:   "匹配码已过期",
	ErrMoodExists:         "今日心情记录已存在",
	ErrInvalidMoodValue:   "心情值无效",
}

// GetErrorMessage 获取错误消息
func GetErrorMessage(errorCode string) string {
	if msg, exists := ErrorMessages[errorCode]; exists {
		return msg
	}
	return "未知错误"
}
