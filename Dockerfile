# MoodTracker API Dockerfile
# 简化版本，注重稳定性

# 使用官方Go镜像作为构建环境
FROM golang:1.24 AS builder

# 设置工作目录
WORKDIR /app

# 复制go mod文件并下载依赖
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 设置构建参数，确保为Linux/AMD64构建
ARG TARGETOS=linux
ARG TARGETARCH=amd64

# 构建应用程序，明确指定目标平台
RUN CGO_ENABLED=0 GOOS=${TARGETOS} GOARCH=${TARGETARCH} go build -a -installsuffix cgo -o bin/server cmd/server/main.go
RUN CGO_ENABLED=0 GOOS=${TARGETOS} GOARCH=${TARGETARCH} go build -a -installsuffix cgo -o bin/migrate cmd/migrate/main.go

# 运行阶段 - 使用Ubuntu基础镜像确保稳定性
FROM ubuntu:22.04

# 安装必要的运行时依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    tzdata \
    && rm -rf /var/lib/apt/lists/*

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/bin/server ./server
COPY --from=builder /app/bin/migrate ./migrate

# 复制必要的文件
COPY migrations ./migrations

# 创建日志目录
RUN mkdir -p /app/logs

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动命令
CMD ["./server"]
