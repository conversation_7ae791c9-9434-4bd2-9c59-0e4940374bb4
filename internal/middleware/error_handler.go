package middleware

import (
	"errors"
	"strings"

	"moodtracker-api/pkg/response"
	"moodtracker-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

// ErrorHandlerMiddleware 全局错误处理中间件
func ErrorHandlerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 检查是否有错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last()
			handleError(c, err.Err)
		}
	}
}

// handleError 处理错误
func handleError(c *gin.Context, err error) {
	// 如果响应已经写入，则不再处理
	if c.Writer.Written() {
		return
	}

	logrus.WithFields(logrus.Fields{
		"method":     c.Request.Method,
		"path":       c.Request.URL.Path,
		"client_ip":  c.<PERSON>(),
		"user_agent": c.Request.UserAgent(),
		"error":      err.Error(),
	}).Error("Request error")

	// 根据错误类型返回相应的HTTP状态码和错误信息
	switch {
	case isValidationError(err):
		handleValidationError(c, err)
	case isBusinessError(err):
		handleBusinessError(c, err)
	case isAuthError(err):
		handleAuthError(c, err)
	case isNotFoundError(err):
		response.NotFound(c, "资源不存在")
	case isForbiddenError(err):
		response.Forbidden(c, "访问被拒绝")
	default:
		response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
	}
}

// isValidationError 检查是否为验证错误
func isValidationError(err error) bool {
	var validationErr validator.ValidationErrors
	return errors.As(err, &validationErr)
}

// handleValidationError 处理验证错误
func handleValidationError(c *gin.Context, err error) {
	var validationErr validator.ValidationErrors
	if errors.As(err, &validationErr) {
		errorMessages := make([]string, 0, len(validationErr))

		for _, fieldErr := range validationErr {
			message := getValidationErrorMessage(fieldErr)
			errorMessages = append(errorMessages, message)
		}

		response.BadRequest(c, strings.Join(errorMessages, "; "))
	} else {
		response.BadRequest(c, "请求参数验证失败")
	}
}

// getValidationErrorMessage 获取验证错误消息
func getValidationErrorMessage(fieldErr validator.FieldError) string {
	field := fieldErr.Field()
	tag := fieldErr.Tag()

	switch tag {
	case "required":
		return field + "是必填字段"
	case "min":
		return field + "长度不能少于" + fieldErr.Param() + "个字符"
	case "max":
		return field + "长度不能超过" + fieldErr.Param() + "个字符"
	case "email":
		return field + "必须是有效的邮箱地址"
	case "uuid":
		return field + "必须是有效的UUID格式"
	case "numeric":
		return field + "必须是数字"
	case "alphanum":
		return field + "只能包含字母和数字"
	case "url":
		return field + "必须是有效的URL"
	default:
		return field + "格式不正确"
	}
}

// isBusinessError 检查是否为业务错误
func isBusinessError(err error) bool {
	errorMsg := err.Error()
	businessErrors := []string{
		"already exists",
		"not found",
		"invalid",
		"expired",
		"must have",
		"permission denied",
		"cannot",
	}

	for _, businessError := range businessErrors {
		if strings.Contains(strings.ToLower(errorMsg), businessError) {
			return true
		}
	}
	return false
}

// handleBusinessError 处理业务错误
func handleBusinessError(c *gin.Context, err error) {
	errorMsg := err.Error()

	switch {
	case strings.Contains(errorMsg, "already exists"):
		response.BadRequest(c, "资源已存在")
	case strings.Contains(errorMsg, "not found"):
		response.NotFound(c, "资源不存在")
	case strings.Contains(errorMsg, "invalid"):
		response.BadRequest(c, "请求参数无效")
	case strings.Contains(errorMsg, "expired"):
		response.BadRequest(c, "资源已过期")
	case strings.Contains(errorMsg, "must have"):
		response.BadRequest(c, "缺少必要条件")
	case strings.Contains(errorMsg, "permission denied"):
		response.Forbidden(c, "权限不足")
	case strings.Contains(errorMsg, "cannot"):
		response.BadRequest(c, "操作不被允许")
	default:
		response.BadRequest(c, "业务处理失败")
	}
}

// isAuthError 检查是否为认证错误
func isAuthError(err error) bool {
	errorMsg := strings.ToLower(err.Error())
	authErrors := []string{
		"unauthorized",
		"invalid credentials",
		"token",
		"authentication",
		"login",
	}

	for _, authError := range authErrors {
		if strings.Contains(errorMsg, authError) {
			return true
		}
	}
	return false
}

// handleAuthError 处理认证错误
func handleAuthError(c *gin.Context, err error) {
	errorMsg := strings.ToLower(err.Error())

	switch {
	case strings.Contains(errorMsg, "invalid credentials"):
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrInvalidCredentials))
	case strings.Contains(errorMsg, "token expired"):
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrTokenExpired))
	case strings.Contains(errorMsg, "token invalid"):
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrTokenInvalid))
	default:
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
	}
}

// isNotFoundError 检查是否为404错误
func isNotFoundError(err error) bool {
	return strings.Contains(strings.ToLower(err.Error()), "not found")
}

// isForbiddenError 检查是否为403错误
func isForbiddenError(err error) bool {
	errorMsg := strings.ToLower(err.Error())
	return strings.Contains(errorMsg, "forbidden") ||
		strings.Contains(errorMsg, "access denied") ||
		strings.Contains(errorMsg, "permission denied")
}

// AbortWithError 中止请求并添加错误
func AbortWithError(c *gin.Context, code int, err error) {
	c.Error(err)
	c.Abort()
}
