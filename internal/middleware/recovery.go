package middleware

import (
	"fmt"
	"net/http"
	"runtime"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"moodtracker-api/pkg/response"
	"moodtracker-api/pkg/utils"
)

// RecoveryMiddleware 恢复中间件
func RecoveryMiddleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		// 获取堆栈信息
		stack := getStackTrace()
		
		// 记录panic日志
		logrus.WithFields(logrus.Fields{
			"method":     c.Request.Method,
			"path":       c.Request.URL.Path,
			"client_ip":  c.ClientIP(),
			"user_agent": c.Request.UserAgent(),
			"panic":      recovered,
			"stack":      stack,
		}).Error("Panic recovered")

		// 返回500错误
		response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
	})
}

// getStackTrace 获取堆栈跟踪信息
func getStackTrace() string {
	var stack []string
	
	// 跳过前几个调用栈帧（runtime和recovery相关）
	for i := 3; i < 15; i++ {
		pc, file, line, ok := runtime.Caller(i)
		if !ok {
			break
		}
		
		// 获取函数名
		fn := runtime.FuncForPC(pc)
		if fn == nil {
			continue
		}
		
		funcName := fn.Name()
		
		// 过滤掉runtime相关的调用
		if strings.Contains(funcName, "runtime.") {
			continue
		}
		
		// 简化文件路径
		if idx := strings.LastIndex(file, "/"); idx >= 0 {
			file = file[idx+1:]
		}
		
		stack = append(stack, fmt.Sprintf("%s:%d %s", file, line, funcName))
	}
	
	return strings.Join(stack, "\n")
}

// DetailedRecoveryMiddleware 详细恢复中间件（开发环境使用）
func DetailedRecoveryMiddleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		// 获取详细堆栈信息
		stack := getDetailedStackTrace()
		
		// 获取请求信息
		requestInfo := getRequestInfo(c)
		
		// 记录详细panic日志
		logrus.WithFields(logrus.Fields{
			"method":       c.Request.Method,
			"path":         c.Request.URL.Path,
			"query":        c.Request.URL.RawQuery,
			"client_ip":    c.ClientIP(),
			"user_agent":   c.Request.UserAgent(),
			"content_type": c.Request.Header.Get("Content-Type"),
			"panic":        recovered,
			"stack":        stack,
			"request_info": requestInfo,
		}).Error("Detailed panic recovered")

		// 在开发环境返回详细错误信息
		if gin.Mode() == gin.DebugMode {
			response.Error(c, http.StatusInternalServerError, fmt.Sprintf("Internal Server Error: %v", recovered))
		} else {
			response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
		}
	})
}

// getDetailedStackTrace 获取详细堆栈跟踪信息
func getDetailedStackTrace() string {
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	return string(buf[:n])
}

// getRequestInfo 获取请求信息
func getRequestInfo(c *gin.Context) map[string]interface{} {
	info := map[string]interface{}{
		"method":       c.Request.Method,
		"url":          c.Request.URL.String(),
		"proto":        c.Request.Proto,
		"content_length": c.Request.ContentLength,
		"host":         c.Request.Host,
		"remote_addr":  c.Request.RemoteAddr,
		"headers":      make(map[string]string),
	}

	// 添加请求头（过滤敏感信息）
	for name, values := range c.Request.Header {
		if !isSensitiveHeader(name) && len(values) > 0 {
			info["headers"].(map[string]string)[name] = values[0]
		}
	}

	// 添加用户信息（如果已认证）
	if userID, exists := c.Get("user_id"); exists {
		info["user_id"] = userID
	}
	if username, exists := c.Get("username"); exists {
		info["username"] = username
	}

	return info
}

// isSensitiveHeader 检查是否为敏感请求头
func isSensitiveHeader(name string) bool {
	sensitiveHeaders := []string{
		"Authorization",
		"Cookie",
		"Set-Cookie",
		"X-Auth-Token",
		"X-API-Key",
	}

	name = strings.ToLower(name)
	for _, sensitive := range sensitiveHeaders {
		if strings.ToLower(sensitive) == name {
			return true
		}
	}
	return false
}
