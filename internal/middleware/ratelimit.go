package middleware

import (
	"fmt"
	"net/http"
	"sync"
	"time"

	"moodtracker-api/pkg/response"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// RateLimiter 限流器接口
type RateLimiter interface {
	Allow(key string) bool
	Reset(key string)
}

// TokenBucket 令牌桶限流器
type TokenBucket struct {
	capacity int           // 桶容量
	tokens   int           // 当前令牌数
	refill   int           // 每次补充的令牌数
	interval time.Duration // 补充间隔
	lastTime time.Time     // 上次补充时间
	mutex    sync.Mutex
}

// NewTokenBucket 创建令牌桶
func NewTokenBucket(capacity, refill int, interval time.Duration) *TokenBucket {
	return &TokenBucket{
		capacity: capacity,
		tokens:   capacity,
		refill:   refill,
		interval: interval,
		lastTime: time.Now(),
	}
}

// Allow 检查是否允许请求
func (tb *TokenBucket) Allow(key string) bool {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()

	now := time.Now()
	// 计算需要补充的令牌数
	elapsed := now.Sub(tb.lastTime)
	tokensToAdd := int(elapsed/tb.interval) * tb.refill

	if tokensToAdd > 0 {
		tb.tokens += tokensToAdd
		if tb.tokens > tb.capacity {
			tb.tokens = tb.capacity
		}
		tb.lastTime = now
	}

	if tb.tokens > 0 {
		tb.tokens--
		return true
	}

	return false
}

// Reset 重置令牌桶
func (tb *TokenBucket) Reset(key string) {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()
	tb.tokens = tb.capacity
	tb.lastTime = time.Now()
}

// InMemoryRateLimiter 内存限流器
type InMemoryRateLimiter struct {
	buckets map[string]*TokenBucket
	mutex   sync.RWMutex
	config  *RateLimitConfig
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Capacity int                       // 桶容量
	Refill   int                       // 每次补充令牌数
	Interval time.Duration             // 补充间隔
	KeyFunc  func(*gin.Context) string // 生成限流key的函数
}

// DefaultRateLimitConfig 默认限流配置
func DefaultRateLimitConfig() *RateLimitConfig {
	return &RateLimitConfig{
		Capacity: 100,         // 100个令牌
		Refill:   10,          // 每次补充10个
		Interval: time.Minute, // 每分钟补充
		KeyFunc: func(c *gin.Context) string {
			return c.ClientIP() // 基于IP限流
		},
	}
}

// UserBasedRateLimitConfig 基于用户的限流配置
func UserBasedRateLimitConfig() *RateLimitConfig {
	return &RateLimitConfig{
		Capacity: 1000,        // 1000个令牌
		Refill:   100,         // 每次补充100个
		Interval: time.Minute, // 每分钟补充
		KeyFunc: func(c *gin.Context) string {
			// 优先使用用户ID，否则使用IP
			if userID, exists := c.Get("user_id"); exists {
				return fmt.Sprintf("user:%s", userID)
			}
			return fmt.Sprintf("ip:%s", c.ClientIP())
		},
	}
}

// NewInMemoryRateLimiter 创建内存限流器
func NewInMemoryRateLimiter(config *RateLimitConfig) *InMemoryRateLimiter {
	if config == nil {
		config = DefaultRateLimitConfig()
	}

	return &InMemoryRateLimiter{
		buckets: make(map[string]*TokenBucket),
		config:  config,
	}
}

// Allow 检查是否允许请求
func (rl *InMemoryRateLimiter) Allow(key string) bool {
	rl.mutex.RLock()
	bucket, exists := rl.buckets[key]
	rl.mutex.RUnlock()

	if !exists {
		rl.mutex.Lock()
		// 双重检查
		if bucket, exists = rl.buckets[key]; !exists {
			bucket = NewTokenBucket(rl.config.Capacity, rl.config.Refill, rl.config.Interval)
			rl.buckets[key] = bucket
		}
		rl.mutex.Unlock()
	}

	return bucket.Allow(key)
}

// Reset 重置限流器
func (rl *InMemoryRateLimiter) Reset(key string) {
	rl.mutex.RLock()
	bucket, exists := rl.buckets[key]
	rl.mutex.RUnlock()

	if exists {
		bucket.Reset(key)
	}
}

// RateLimitMiddleware 限流中间件
func RateLimitMiddleware(limiter RateLimiter, config *RateLimitConfig) gin.HandlerFunc {
	if config == nil {
		config = DefaultRateLimitConfig()
	}

	return func(c *gin.Context) {
		key := config.KeyFunc(c)

		if !limiter.Allow(key) {
			logrus.WithFields(logrus.Fields{
				"key":       key,
				"client_ip": c.ClientIP(),
				"path":      c.Request.URL.Path,
				"method":    c.Request.Method,
			}).Warn("Rate limit exceeded")

			response.Error(c, http.StatusTooManyRequests, "请求过于频繁，请稍后再试")
			c.Abort()
			return
		}

		c.Next()
	}
}

// CleanupExpiredBuckets 清理过期的令牌桶
func (rl *InMemoryRateLimiter) CleanupExpiredBuckets() {
	ticker := time.NewTicker(time.Hour) // 每小时清理一次
	go func() {
		for range ticker.C {
			rl.mutex.Lock()
			now := time.Now()
			for key, bucket := range rl.buckets {
				// 如果令牌桶超过2小时没有活动，则删除
				if now.Sub(bucket.lastTime) > 2*time.Hour {
					delete(rl.buckets, key)
				}
			}
			rl.mutex.Unlock()
		}
	}()
}
