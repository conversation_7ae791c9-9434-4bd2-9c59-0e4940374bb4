package middleware

import (
	"bytes"
	"io"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// LoggingMiddleware 日志中间件
func LoggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// 自定义日志格式
		logrus.WithFields(logrus.Fields{
			"status_code":  param.StatusCode,
			"latency":      param.Latency,
			"client_ip":    param.ClientIP,
			"method":       param.Method,
			"path":         param.Path,
			"user_agent":   param.Request.UserAgent(),
			"error":        param.ErrorMessage,
			"body_size":    param.BodySize,
			"timestamp":    param.TimeStamp.Format(time.RFC3339),
		}).Info("HTTP Request")

		return ""
	})
}

// RequestResponseLoggingMiddleware 请求响应详细日志中间件
func RequestResponseLoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录请求开始时间
		start := time.Now()

		// 读取请求体（如果需要）
		var requestBody []byte
		if c.Request.Body != nil && c.Request.ContentLength > 0 && c.Request.ContentLength < 1024*10 { // 限制10KB
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 创建响应写入器包装器
		writer := &responseWriter{
			ResponseWriter: c.Writer,
			body:          &bytes.Buffer{},
		}
		c.Writer = writer

		// 处理请求
		c.Next()

		// 计算处理时间
		latency := time.Since(start)

		// 获取用户信息（如果已认证）
		userID, _ := c.Get("user_id")
		username, _ := c.Get("username")

		// 记录详细日志
		fields := logrus.Fields{
			"method":       c.Request.Method,
			"path":         c.Request.URL.Path,
			"query":        c.Request.URL.RawQuery,
			"status_code":  c.Writer.Status(),
			"latency":      latency,
			"client_ip":    c.ClientIP(),
			"user_agent":   c.Request.UserAgent(),
			"content_type": c.Request.Header.Get("Content-Type"),
		}

		// 添加用户信息（如果存在）
		if userID != nil {
			fields["user_id"] = userID
		}
		if username != nil {
			fields["username"] = username
		}

		// 添加请求体（如果不为空且不敏感）
		if len(requestBody) > 0 && !isSensitivePath(c.Request.URL.Path) {
			fields["request_body"] = string(requestBody)
		}

		// 添加响应体（如果不太大）
		if writer.body.Len() < 1024*5 { // 限制5KB
			fields["response_body"] = writer.body.String()
		}

		// 根据状态码选择日志级别
		switch {
		case c.Writer.Status() >= 500:
			logrus.WithFields(fields).Error("HTTP Request - Server Error")
		case c.Writer.Status() >= 400:
			logrus.WithFields(fields).Warn("HTTP Request - Client Error")
		case c.Writer.Status() >= 300:
			logrus.WithFields(fields).Info("HTTP Request - Redirect")
		default:
			logrus.WithFields(fields).Info("HTTP Request - Success")
		}
	}
}

// responseWriter 响应写入器包装器
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// isSensitivePath 检查是否为敏感路径
func isSensitivePath(path string) bool {
	sensitivePaths := []string{
		"/api/v1/auth/login",
		"/api/v1/auth/register",
		"/api/v1/auth/refresh-token",
	}

	for _, sensitivePath := range sensitivePaths {
		if path == sensitivePath {
			return true
		}
	}
	return false
}

// ErrorLoggingMiddleware 错误日志中间件
func ErrorLoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 检查是否有错误
		if len(c.Errors) > 0 {
			for _, err := range c.Errors {
				logrus.WithFields(logrus.Fields{
					"method":     c.Request.Method,
					"path":       c.Request.URL.Path,
					"client_ip":  c.ClientIP(),
					"user_agent": c.Request.UserAgent(),
					"error_type": err.Type,
				}).Error("Request Error: " + err.Error())
			}
		}
	}
}
