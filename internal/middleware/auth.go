package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"moodtracker-api/internal/services"
	"moodtracker-api/pkg/response"
	"moodtracker-api/pkg/utils"
)

// AuthMiddleware 认证中间件
func AuthMiddleware(authService *services.AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			logrus.Warn("Missing authorization header")
			response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
			c.Abort()
			return
		}

		// 解析Bearer token
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			logrus.Warn("Invalid authorization header format")
			response.Unauthorized(c, utils.GetErrorMessage(utils.ErrTokenInvalid))
			c.Abort()
			return
		}

		accessToken := parts[1]

		// 验证访问令牌
		claims, err := authService.ValidateAccessToken(accessToken)
		if err != nil {
			logrus.Warnf("Token validation failed: %v", err)
			if strings.Contains(err.Error(), "expired") {
				response.Unauthorized(c, utils.GetErrorMessage(utils.ErrTokenExpired))
			} else {
				response.Unauthorized(c, utils.GetErrorMessage(utils.ErrTokenInvalid))
			}
			c.Abort()
			return
		}

		// 将用户信息设置到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("device_id", claims.DeviceID)

		// 继续处理请求
		c.Next()
	}
}

// OptionalAuthMiddleware 可选认证中间件（不强制要求认证）
func OptionalAuthMiddleware(authService *services.AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			// 没有认证头，继续处理但不设置用户信息
			c.Next()
			return
		}

		// 解析Bearer token
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			// 格式错误，继续处理但不设置用户信息
			c.Next()
			return
		}

		accessToken := parts[1]

		// 验证访问令牌
		claims, err := authService.ValidateAccessToken(accessToken)
		if err != nil {
			// 验证失败，继续处理但不设置用户信息
			logrus.Debugf("Optional auth failed: %v", err)
			c.Next()
			return
		}

		// 将用户信息设置到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("device_id", claims.DeviceID)

		// 继续处理请求
		c.Next()
	}
}
