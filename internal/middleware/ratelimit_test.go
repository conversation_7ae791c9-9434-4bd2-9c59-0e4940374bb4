package middleware

import (
	"testing"
	"time"

	"github.com/gin-gonic/gin"
)

func TestTokenBucket_Allow(t *testing.T) {
	capacity := 5
	refill := 2
	interval := time.Second

	bucket := NewTokenBucket(capacity, refill, interval)

	// Should allow up to capacity requests
	for i := 0; i < capacity; i++ {
		if !bucket.Allow("test") {
			t.<PERSON><PERSON><PERSON>("Request %d should be allowed", i+1)
		}
	}

	// Next request should be denied
	if bucket.Allow("test") {
		t.<PERSON>rror("Request should be denied when bucket is empty")
	}
}

func TestTokenBucket_Refill(t *testing.T) {
	capacity := 5
	refill := 2
	interval := 100 * time.Millisecond

	bucket := NewTokenBucket(capacity, refill, interval)

	// Exhaust the bucket
	for i := 0; i < capacity; i++ {
		bucket.Allow("test")
	}

	// Should be denied
	if bucket.Allow("test") {
		t.Error("Request should be denied when bucket is empty")
	}

	// Wait for refill
	time.Sleep(150 * time.Millisecond)

	// Should allow refill amount of requests
	for i := 0; i < refill; i++ {
		if !bucket.Allow("test") {
			t.<PERSON><PERSON>("Request %d should be allowed after refill", i+1)
		}
	}

	// Should be denied again
	if bucket.Allow("test") {
		t.Error("Request should be denied after using refilled tokens")
	}
}

func TestTokenBucket_Reset(t *testing.T) {
	capacity := 5
	refill := 2
	interval := time.Second

	bucket := NewTokenBucket(capacity, refill, interval)

	// Exhaust the bucket
	for i := 0; i < capacity; i++ {
		bucket.Allow("test")
	}

	// Should be denied
	if bucket.Allow("test") {
		t.Error("Request should be denied when bucket is empty")
	}

	// Reset the bucket
	bucket.Reset("test")

	// Should allow requests again
	if !bucket.Allow("test") {
		t.Error("Request should be allowed after reset")
	}
}

func TestInMemoryRateLimiter_Allow(t *testing.T) {
	config := &RateLimitConfig{
		Capacity: 3,
		Refill:   1,
		Interval: time.Second,
		KeyFunc: func(c *gin.Context) string {
			return "test-key"
		},
	}

	limiter := NewInMemoryRateLimiter(config)

	// Should allow up to capacity requests
	for i := 0; i < config.Capacity; i++ {
		if !limiter.Allow("test-key") {
			t.Errorf("Request %d should be allowed", i+1)
		}
	}

	// Next request should be denied
	if limiter.Allow("test-key") {
		t.Error("Request should be denied when limit exceeded")
	}
}

func TestInMemoryRateLimiter_DifferentKeys(t *testing.T) {
	config := &RateLimitConfig{
		Capacity: 2,
		Refill:   1,
		Interval: time.Second,
		KeyFunc: func(c *gin.Context) string {
			return "test-key"
		},
	}

	limiter := NewInMemoryRateLimiter(config)

	// Exhaust limit for key1
	limiter.Allow("key1")
	limiter.Allow("key1")

	// key1 should be denied
	if limiter.Allow("key1") {
		t.Error("key1 should be denied")
	}

	// key2 should still be allowed
	if !limiter.Allow("key2") {
		t.Error("key2 should be allowed")
	}
}

func TestInMemoryRateLimiter_Reset(t *testing.T) {
	config := &RateLimitConfig{
		Capacity: 2,
		Refill:   1,
		Interval: time.Second,
		KeyFunc: func(c *gin.Context) string {
			return "test-key"
		},
	}

	limiter := NewInMemoryRateLimiter(config)

	// Exhaust the limit
	limiter.Allow("test-key")
	limiter.Allow("test-key")

	// Should be denied
	if limiter.Allow("test-key") {
		t.Error("Request should be denied")
	}

	// Reset the limiter
	limiter.Reset("test-key")

	// Should be allowed again
	if !limiter.Allow("test-key") {
		t.Error("Request should be allowed after reset")
	}
}

func TestDefaultRateLimitConfig(t *testing.T) {
	config := DefaultRateLimitConfig()

	if config.Capacity != 100 {
		t.Errorf("Expected capacity 100, got %d", config.Capacity)
	}

	if config.Refill != 10 {
		t.Errorf("Expected refill 10, got %d", config.Refill)
	}

	if config.Interval != time.Minute {
		t.Errorf("Expected interval 1 minute, got %v", config.Interval)
	}

	if config.KeyFunc == nil {
		t.Error("KeyFunc should not be nil")
	}
}

func TestUserBasedRateLimitConfig(t *testing.T) {
	config := UserBasedRateLimitConfig()

	if config.Capacity != 1000 {
		t.Errorf("Expected capacity 1000, got %d", config.Capacity)
	}

	if config.Refill != 100 {
		t.Errorf("Expected refill 100, got %d", config.Refill)
	}

	if config.Interval != time.Minute {
		t.Errorf("Expected interval 1 minute, got %v", config.Interval)
	}

	if config.KeyFunc == nil {
		t.Error("KeyFunc should not be nil")
	}
}
