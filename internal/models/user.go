package models

import (
	"time"
)

// User 用户模型
type User struct {
	UserID      string     `json:"userId" db:"user_id"`
	Username    string     `json:"username" db:"username" validate:"required,min=3,max=20,alphanum"`
	PasswordHash string    `json:"-" db:"password_hash"`
	Nickname    *string    `json:"nickname" db:"nickname" validate:"omitempty,max=100"`
	AvatarURL   *string    `json:"avatarUrl" db:"avatar_url" validate:"omitempty,url,max=500"`
	CreatedAt   time.Time  `json:"createdAt" db:"created_at"`
	UpdatedAt   time.Time  `json:"updatedAt" db:"updated_at"`
	LastLoginAt *time.Time `json:"lastLoginAt" db:"last_login_at"`
}

// UserRegisterRequest 用户注册请求
type UserRegisterRequest struct {
	Username string  `json:"username" validate:"required,min=3,max=20,alphanum"`
	Password string  `json:"password" validate:"required,min=6,max=50"`
	Nickname *string `json:"nickname" validate:"omitempty,max=100"`
}

// UserLoginRequest 用户登录请求
type UserLoginRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
	DeviceID string `json:"deviceId" validate:"required,uuid"`
}

// UserResponse 用户响应（不包含敏感信息）
type UserResponse struct {
	UserID      string     `json:"userId"`
	Username    string     `json:"username"`
	Nickname    *string    `json:"nickname"`
	AvatarURL   *string    `json:"avatarUrl"`
	CreatedAt   time.Time  `json:"createdAt"`
	LastLoginAt *time.Time `json:"lastLoginAt"`
}

// ToResponse 转换为响应格式
func (u *User) ToResponse() *UserResponse {
	return &UserResponse{
		UserID:      u.UserID,
		Username:    u.Username,
		Nickname:    u.Nickname,
		AvatarURL:   u.AvatarURL,
		CreatedAt:   u.CreatedAt,
		LastLoginAt: u.LastLoginAt,
	}
}
