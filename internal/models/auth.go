package models

import (
	"time"
)

// RefreshToken 刷新令牌模型
type RefreshToken struct {
	TokenID   string    `json:"tokenId" db:"token_id"`
	UserID    string    `json:"userId" db:"user_id"`
	TokenHash string    `json:"-" db:"token_hash"`
	DeviceID  *string   `json:"deviceId" db:"device_id"`
	ExpiresAt time.Time `json:"expiresAt" db:"expires_at"`
	CreatedAt time.Time `json:"createdAt" db:"created_at"`
}

// AuthResponse 认证响应
type AuthResponse struct {
	User         *UserResponse `json:"user"`
	AccessToken  string        `json:"accessToken"`
	RefreshToken string        `json:"refreshToken"`
	ExpiresAt    time.Time     `json:"expiresAt"`
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refreshToken" validate:"required"`
}

// RefreshTokenResponse 刷新令牌响应
type RefreshTokenResponse struct {
	AccessToken  string    `json:"accessToken"`
	RefreshToken string    `json:"refreshToken"`
	ExpiresAt    time.Time `json:"expiresAt"`
}

// JWTClaims JWT声明
type JWTClaims struct {
	UserID   string `json:"sub"`
	Username string `json:"username"`
	DeviceID string `json:"device_id"`
	IssuedAt int64  `json:"iat"`
	ExpiresAt int64 `json:"exp"`
}

// SyncChangesRequest 同步变更请求
type SyncChangesRequest struct {
	LastSyncTime *string `form:"lastSyncTime" validate:"omitempty" time_format:"2006-01-02T15:04:05Z07:00"`
}

// SyncChangesResponse 同步变更响应
type SyncChangesResponse struct {
	Changes      []*MoodResponse `json:"changes"`
	LastSyncTime time.Time       `json:"lastSyncTime"`
}

// SyncUploadRequest 同步上传请求
type SyncUploadRequest struct {
	Changes []*SyncMoodData `json:"changes" validate:"required,dive"`
}

// SyncMoodData 同步心情数据
type SyncMoodData struct {
	EntryID   string     `json:"entryId" validate:"required,uuid"`
	Date      string     `json:"date" validate:"required" time_format:"2006-01-02"`
	Mood      int        `json:"mood" validate:"required,min=1,max=12"`
	Note      *string    `json:"note" validate:"omitempty,max=1000"`
	CreatedAt time.Time  `json:"createdAt" validate:"required"`
	UpdatedAt time.Time  `json:"updatedAt" validate:"required"`
	IsDeleted bool       `json:"isDeleted"`
}

// SyncUploadResponse 同步上传响应
type SyncUploadResponse struct {
	Processed int                    `json:"processed"`
	Conflicts []*SyncConflictData    `json:"conflicts"`
	Errors    []*SyncErrorData       `json:"errors"`
}

// SyncConflictData 同步冲突数据
type SyncConflictData struct {
	EntryID       string    `json:"entryId"`
	ConflictType  string    `json:"conflictType"`
	ServerVersion time.Time `json:"serverVersion"`
	ClientVersion time.Time `json:"clientVersion"`
}

// SyncErrorData 同步错误数据
type SyncErrorData struct {
	EntryID string `json:"entryId"`
	Error   string `json:"error"`
}
