package models

import (
	"time"
)

// CoupleStatus 情侣关系状态
type CoupleStatus string

const (
	CoupleStatusWaiting CoupleStatus = "waiting"
	CoupleStatusMatched CoupleStatus = "matched"
)

// Couple 情侣关系模型
type Couple struct {
	CoupleID         string        `json:"coupleId" db:"couple_id"`
	User1ID          string        `json:"-" db:"user1_id"`
	User2ID          *string       `json:"-" db:"user2_id"`
	MatchCode        *string       `json:"matchCode" db:"match_code"`
	RelationshipName *string       `json:"relationshipName" db:"relationship_name"`
	Status           CoupleStatus  `json:"status" db:"status"`
	CreatedAt        time.Time     `json:"createdAt" db:"created_at"`
	MatchedAt        *time.Time    `json:"matchedAt" db:"matched_at"`
	
	// 关联的用户信息（用于响应）
	User1 *UserResponse `json:"user1,omitempty"`
	User2 *UserResponse `json:"user2,omitempty"`
}

// CoupleCreateRequest 创建情侣关系请求
type CoupleCreateRequest struct {
	RelationshipName string `json:"relationshipName" validate:"required,max=100"`
}

// CoupleJoinRequest 加入情侣关系请求
type CoupleJoinRequest struct {
	MatchCode string `json:"matchCode" validate:"required,len=8,numeric"`
}

// CoupleResponse 情侣关系响应
type CoupleResponse struct {
	CoupleID         string        `json:"coupleId"`
	MatchCode        *string       `json:"matchCode"`
	User1            *UserResponse `json:"user1"`
	User2            *UserResponse `json:"user2"`
	RelationshipName *string       `json:"relationshipName"`
	Status           CoupleStatus  `json:"status"`
	CreatedAt        time.Time     `json:"createdAt"`
	MatchedAt        *time.Time    `json:"matchedAt"`
}

// ToResponse 转换为响应格式
func (c *Couple) ToResponse() *CoupleResponse {
	return &CoupleResponse{
		CoupleID:         c.CoupleID,
		MatchCode:        c.MatchCode,
		User1:            c.User1,
		User2:            c.User2,
		RelationshipName: c.RelationshipName,
		Status:           c.Status,
		CreatedAt:        c.CreatedAt,
		MatchedAt:        c.MatchedAt,
	}
}
