package models

import (
	"time"
)

// SyncStatus 同步状态
type SyncStatus string

const (
	SyncStatusPending SyncStatus = "PENDING"
	SyncStatusSynced  SyncStatus = "SYNCED"
	SyncStatusFailed  SyncStatus = "FAILED"
)

// Mood 心情记录模型
type Mood struct {
	EntryID    string     `json:"entryId" db:"entry_id"`
	UserID     string     `json:"userId" db:"user_id"`
	CoupleID   string     `json:"coupleId" db:"couple_id"`
	Date       time.Time  `json:"date" db:"date"`
	MoodValue  int        `json:"mood" db:"mood" validate:"required,min=1,max=12"`
	Note       *string    `json:"note" db:"note" validate:"omitempty,max=1000"`
	CreatedAt  time.Time  `json:"createdAt" db:"created_at"`
	UpdatedAt  time.Time  `json:"updatedAt" db:"updated_at"`
	SyncStatus SyncStatus `json:"syncStatus" db:"sync_status"`
	IsDeleted  bool       `json:"isDeleted" db:"is_deleted"`
}

// MoodCreateRequest 创建心情记录请求
type MoodCreateRequest struct {
	Date string  `json:"date" validate:"required" time_format:"2006-01-02"`
	Mood int     `json:"mood" validate:"required,min=1,max=12"`
	Note *string `json:"note" validate:"omitempty,max=1000"`
}

// MoodUpdateRequest 更新心情记录请求
type MoodUpdateRequest struct {
	Mood int     `json:"mood" validate:"required,min=1,max=12"`
	Note *string `json:"note" validate:"omitempty,max=1000"`
}

// MoodQueryParams 心情记录查询参数
type MoodQueryParams struct {
	StartDate *string `form:"startDate" validate:"omitempty" time_format:"2006-01-02"`
	EndDate   *string `form:"endDate" validate:"omitempty" time_format:"2006-01-02"`
	Limit     int     `form:"limit" validate:"omitempty,min=1,max=1000"`
	Offset    int     `form:"offset" validate:"omitempty,min=0"`
}

// MoodResponse 心情记录响应
type MoodResponse struct {
	EntryID    string     `json:"entryId"`
	UserID     string     `json:"userId"`
	CoupleID   string     `json:"coupleId"`
	Date       string     `json:"date"` // 格式化为 YYYY-MM-DD
	Mood       int        `json:"mood"`
	Note       *string    `json:"note"`
	CreatedAt  time.Time  `json:"createdAt"`
	UpdatedAt  time.Time  `json:"updatedAt"`
	SyncStatus SyncStatus `json:"syncStatus"`
	IsDeleted  bool       `json:"isDeleted"`
}

// ToResponse 转换为响应格式
func (m *Mood) ToResponse() *MoodResponse {
	return &MoodResponse{
		EntryID:    m.EntryID,
		UserID:     m.UserID,
		CoupleID:   m.CoupleID,
		Date:       m.Date.Format("2006-01-02"),
		Mood:       m.MoodValue,
		Note:       m.Note,
		CreatedAt:  m.CreatedAt,
		UpdatedAt:  m.UpdatedAt,
		SyncStatus: m.SyncStatus,
		IsDeleted:  m.IsDeleted,
	}
}
