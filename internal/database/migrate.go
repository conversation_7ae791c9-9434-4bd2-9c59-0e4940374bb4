package database

import (
	"fmt"
	"io/ioutil"
	"path/filepath"
	"sort"
	"strings"

	"github.com/sirupsen/logrus"
)

// Migration 数据库迁移
type Migration struct {
	Version string
	Name    string
	SQL     string
}

// Migrator 数据库迁移器
type Migrator struct {
	db *DB
}

// NewMigrator 创建迁移器
func NewMigrator(db *DB) *Migrator {
	return &Migrator{db: db}
}

// CreateMigrationsTable 创建迁移记录表
func (m *Migrator) CreateMigrationsTable() error {
	query := `
	CREATE TABLE IF NOT EXISTS migrations (
		id INT AUTO_INCREMENT PRIMARY KEY,
		version VARCHAR(255) NOT NULL UNIQUE,
		name VARCHAR(255) NOT NULL,
		executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
	`

	_, err := m.db.Exec(query)
	if err != nil {
		return fmt.Errorf("failed to create migrations table: %w", err)
	}

	logrus.Info("Migrations table created successfully")
	return nil
}

// LoadMigrations 从目录加载迁移文件
func (m *Migrator) LoadMigrations(migrationsDir string) ([]*Migration, error) {
	files, err := ioutil.ReadDir(migrationsDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read migrations directory: %w", err)
	}

	var migrations []*Migration
	for _, file := range files {
		if !strings.HasSuffix(file.Name(), ".sql") {
			continue
		}

		// 解析文件名格式: 001_create_tables.sql
		parts := strings.SplitN(file.Name(), "_", 2)
		if len(parts) != 2 {
			logrus.Warnf("Skipping migration file with invalid name format: %s", file.Name())
			continue
		}

		version := parts[0]
		name := strings.TrimSuffix(parts[1], ".sql")

		// 读取SQL内容
		filePath := filepath.Join(migrationsDir, file.Name())
		content, err := ioutil.ReadFile(filePath)
		if err != nil {
			return nil, fmt.Errorf("failed to read migration file %s: %w", file.Name(), err)
		}

		migrations = append(migrations, &Migration{
			Version: version,
			Name:    name,
			SQL:     string(content),
		})
	}

	// 按版本号排序
	sort.Slice(migrations, func(i, j int) bool {
		return migrations[i].Version < migrations[j].Version
	})

	return migrations, nil
}

// GetExecutedMigrations 获取已执行的迁移
func (m *Migrator) GetExecutedMigrations() (map[string]bool, error) {
	query := "SELECT version FROM migrations"
	rows, err := m.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query executed migrations: %w", err)
	}
	defer rows.Close()

	executed := make(map[string]bool)
	for rows.Next() {
		var version string
		if err := rows.Scan(&version); err != nil {
			return nil, fmt.Errorf("failed to scan migration version: %w", err)
		}
		executed[version] = true
	}

	return executed, nil
}

// ExecuteMigration 执行单个迁移
func (m *Migrator) ExecuteMigration(migration *Migration) error {
	// 开始事务
	tx, err := m.db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// 执行迁移SQL - 分割多个语句
	statements := strings.Split(migration.SQL, ";")
	for _, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" || strings.HasPrefix(stmt, "--") {
			continue
		}
		_, err = tx.Exec(stmt)
		if err != nil {
			return fmt.Errorf("failed to execute migration %s statement '%s': %w", migration.Version, stmt, err)
		}
	}

	// 记录迁移
	_, err = tx.Exec("INSERT INTO migrations (version, name) VALUES (?, ?)", migration.Version, migration.Name)
	if err != nil {
		return fmt.Errorf("failed to record migration %s: %w", migration.Version, err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit migration %s: %w", migration.Version, err)
	}

	logrus.Infof("Migration %s (%s) executed successfully", migration.Version, migration.Name)
	return nil
}

// Migrate 执行所有待执行的迁移
func (m *Migrator) Migrate(migrationsDir string) error {
	// 创建迁移表
	if err := m.CreateMigrationsTable(); err != nil {
		return err
	}

	// 加载迁移文件
	migrations, err := m.LoadMigrations(migrationsDir)
	if err != nil {
		return err
	}

	// 获取已执行的迁移
	executed, err := m.GetExecutedMigrations()
	if err != nil {
		return err
	}

	// 执行待执行的迁移
	executedCount := 0
	for _, migration := range migrations {
		if executed[migration.Version] {
			logrus.Debugf("Migration %s already executed, skipping", migration.Version)
			continue
		}

		if err := m.ExecuteMigration(migration); err != nil {
			return err
		}
		executedCount++
	}

	if executedCount == 0 {
		logrus.Info("No new migrations to execute")
	} else {
		logrus.Infof("Successfully executed %d migrations", executedCount)
	}

	return nil
}
