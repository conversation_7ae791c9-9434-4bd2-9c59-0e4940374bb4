package handlers

import (
	"strings"

	"moodtracker-api/internal/models"
	"moodtracker-api/internal/services"
	"moodtracker-api/pkg/response"
	"moodtracker-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	authService *services.AuthService
	validator   *validator.Validate
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(authService *services.AuthService) *AuthHandler {
	return &AuthHandler{
		authService: authService,
		validator:   validator.New(),
	}
}

// Register 用户注册
func (h *AuthHandler) Register(c *gin.Context) {
	var req models.UserRegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.Warnf("Invalid register request: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 验证请求参数
	if err := h.validator.Struct(&req); err != nil {
		logrus.Warnf("Register validation failed: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 执行注册
	authResp, err := h.authService.Register(&req)
	if err != nil {
		logrus.Errorf("Register failed: %v", err)
		if strings.Contains(err.Error(), "username already exists") {
			response.BadRequest(c, utils.GetErrorMessage(utils.ErrUserExists))
			return
		}
		response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
		return
	}

	logrus.Infof("User registered successfully: %s", req.Username)
	response.Success(c, authResp, "注册成功")
}

// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.UserLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.Warnf("Invalid login request: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 验证请求参数
	if err := h.validator.Struct(&req); err != nil {
		logrus.Warnf("Login validation failed: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 执行登录
	authResp, err := h.authService.Login(&req)
	if err != nil {
		logrus.Warnf("Login failed for user %s: %v", req.Username, err)
		if strings.Contains(err.Error(), "invalid credentials") {
			response.Unauthorized(c, utils.GetErrorMessage(utils.ErrInvalidCredentials))
			return
		}
		response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
		return
	}

	logrus.Infof("User logged in successfully: %s", req.Username)
	response.Success(c, authResp, "登录成功")
}

// RefreshToken 刷新访问令牌
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	// 从Authorization头获取刷新令牌
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
		return
	}

	// 解析Bearer token
	parts := strings.SplitN(authHeader, " ", 2)
	if len(parts) != 2 || parts[0] != "Bearer" {
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrTokenInvalid))
		return
	}

	refreshToken := parts[1]

	// 刷新令牌
	tokenResp, err := h.authService.RefreshToken(refreshToken)
	if err != nil {
		logrus.Warnf("Refresh token failed: %v", err)
		if strings.Contains(err.Error(), "expired") {
			response.Unauthorized(c, utils.GetErrorMessage(utils.ErrTokenExpired))
			return
		}
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrTokenInvalid))
		return
	}

	logrus.Info("Token refreshed successfully")
	response.Success(c, tokenResp, "令牌刷新成功")
}

// Logout 用户登出
func (h *AuthHandler) Logout(c *gin.Context) {
	// 从Authorization头获取刷新令牌
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) == 2 && parts[0] == "Bearer" {
			refreshToken := parts[1]
			err := h.authService.Logout(refreshToken)
			if err != nil {
				logrus.Warnf("Logout failed: %v", err)
			}
		}
	}

	logrus.Info("User logged out")
	response.Success(c, nil, "登出成功")
}

// GetCurrentUser 获取当前用户信息
func (h *AuthHandler) GetCurrentUser(c *gin.Context) {
	// 从上下文获取用户信息（由认证中间件设置）
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
		return
	}

	username, exists := c.Get("username")
	if !exists {
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
		return
	}

	user := map[string]interface{}{
		"userId":   userID,
		"username": username,
	}

	response.Success(c, user, "获取用户信息成功")
}
