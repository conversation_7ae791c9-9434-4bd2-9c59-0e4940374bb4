package handlers

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
	"moodtracker-api/internal/models"
	"moodtracker-api/internal/services"
	"moodtracker-api/pkg/response"
	"moodtracker-api/pkg/utils"
)

// CoupleHandler 情侣处理器
type CoupleHandler struct {
	coupleService *services.CoupleService
	validator     *validator.Validate
}

// NewCoupleHandler 创建情侣处理器
func NewCoupleHandler(coupleService *services.CoupleService) *CoupleHandler {
	return &CoupleHandler{
		coupleService: coupleService,
		validator:     validator.New(),
	}
}

// CreateCouple 创建情侣关系
func (h *CoupleHandler) CreateCouple(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
		return
	}

	var req models.CoupleCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.Warnf("Invalid create couple request: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 验证请求参数
	if err := h.validator.Struct(&req); err != nil {
		logrus.Warnf("Create couple validation failed: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 创建情侣关系
	couple, err := h.coupleService.CreateCouple(userID.(string), &req)
	if err != nil {
		logrus.Errorf("Create couple failed: %v", err)
		if strings.Contains(err.Error(), "already has a couple relationship") {
			response.BadRequest(c, utils.GetErrorMessage(utils.ErrCoupleExists))
			return
		}
		response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
		return
	}

	logrus.Infof("Couple created successfully by user: %s", userID)
	response.Success(c, couple.ToResponse(), "情侣关系创建成功")
}

// JoinCouple 加入情侣关系
func (h *CoupleHandler) JoinCouple(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
		return
	}

	var req models.CoupleJoinRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.Warnf("Invalid join couple request: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 验证请求参数
	if err := h.validator.Struct(&req); err != nil {
		logrus.Warnf("Join couple validation failed: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 加入情侣关系
	couple, err := h.coupleService.JoinCouple(userID.(string), &req)
	if err != nil {
		logrus.Errorf("Join couple failed: %v", err)
		if strings.Contains(err.Error(), "already has a couple relationship") {
			response.BadRequest(c, utils.GetErrorMessage(utils.ErrCoupleExists))
			return
		}
		if strings.Contains(err.Error(), "invalid match code") {
			response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidMatchCode))
			return
		}
		if strings.Contains(err.Error(), "not waiting for match") {
			response.BadRequest(c, utils.GetErrorMessage(utils.ErrMatchCodeExpired))
			return
		}
		response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
		return
	}

	logrus.Infof("User %s joined couple successfully", userID)
	response.Success(c, couple.ToResponse(), "成功加入情侣关系")
}

// GetCoupleInfo 获取情侣关系信息
func (h *CoupleHandler) GetCoupleInfo(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
		return
	}

	// 获取情侣关系信息
	couple, err := h.coupleService.GetCoupleInfo(userID.(string))
	if err != nil {
		if strings.Contains(err.Error(), "couple not found") {
			response.Success(c, nil, "用户暂无情侣关系")
			return
		}
		logrus.Errorf("Get couple info failed: %v", err)
		response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
		return
	}

	response.Success(c, couple.ToResponse(), "获取情侣信息成功")
}

// LeaveCouple 离开情侣关系
func (h *CoupleHandler) LeaveCouple(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
		return
	}

	// 离开情侣关系
	err := h.coupleService.LeaveCouple(userID.(string))
	if err != nil {
		if strings.Contains(err.Error(), "no couple relationship found") {
			response.NotFound(c, "未找到情侣关系")
			return
		}
		logrus.Errorf("Leave couple failed: %v", err)
		response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
		return
	}

	logrus.Infof("User %s left couple successfully", userID)
	response.Success(c, nil, "成功离开情侣关系")
}
