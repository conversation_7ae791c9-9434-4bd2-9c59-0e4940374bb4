package handlers

import (
	"strings"

	"moodtracker-api/internal/models"
	"moodtracker-api/internal/services"
	"moodtracker-api/pkg/response"
	"moodtracker-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

// MoodHandler 心情处理器
type MoodHandler struct {
	moodService *services.MoodService
	validator   *validator.Validate
}

// NewMoodHandler 创建心情处理器
func NewMoodHandler(moodService *services.MoodService) *MoodHandler {
	return &MoodHandler{
		moodService: moodService,
		validator:   validator.New(),
	}
}

// CreateMood 创建心情记录
func (h *MoodHandler) CreateMood(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
		return
	}

	var req models.MoodCreateRequest
	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		logrus.Warnf("Invalid create mood request: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 验证请求参数
	if err := h.validator.Struct(&req); err != nil {
		logrus.Warnf("Create mood validation failed: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 验证心情值范围
	if req.Mood < 1 || req.Mood > 12 {
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidMoodValue))
		return
	}

	// 创建心情记录
	mood, err := h.moodService.CreateMood(userID.(string), &req)
	if err != nil {
		logrus.Errorf("Create mood failed: %v", err)
		if strings.Contains(err.Error(), "must have a couple relationship") {
			response.BadRequest(c, "必须先建立情侣关系才能记录心情")
			return
		}
		if strings.Contains(err.Error(), "already exists for this date") {
			response.BadRequest(c, utils.GetErrorMessage(utils.ErrMoodExists))
			return
		}
		if strings.Contains(err.Error(), "invalid date format") {
			response.BadRequest(c, "日期格式无效，请使用YYYY-MM-DD格式")
			return
		}
		response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
		return
	}

	logrus.Infof("Mood created successfully by user: %s", userID)
	response.Success(c, mood.ToResponse(), "心情记录创建成功")
}

// UpdateMood 更新心情记录
func (h *MoodHandler) UpdateMood(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
		return
	}

	// 获取entryId参数
	entryID := c.Param("entryId")
	if entryID == "" {
		response.BadRequest(c, "缺少心情记录ID")
		return
	}

	var req models.MoodUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.Warnf("Invalid update mood request: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 验证请求参数
	if err := h.validator.Struct(&req); err != nil {
		logrus.Warnf("Update mood validation failed: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 验证心情值范围
	if req.Mood < 1 || req.Mood > 12 {
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidMoodValue))
		return
	}

	// 更新心情记录
	mood, err := h.moodService.UpdateMood(userID.(string), entryID, &req)
	if err != nil {
		logrus.Errorf("Update mood failed: %v", err)
		if strings.Contains(err.Error(), "not found") {
			response.NotFound(c, "心情记录不存在")
			return
		}
		if strings.Contains(err.Error(), "permission denied") {
			response.Forbidden(c, "只能更新自己的心情记录")
			return
		}
		if strings.Contains(err.Error(), "deleted mood entry") {
			response.BadRequest(c, "无法更新已删除的心情记录")
			return
		}
		response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
		return
	}

	logrus.Infof("Mood updated successfully: %s by user: %s", entryID, userID)
	response.Success(c, mood.ToResponse(), "心情记录更新成功")
}

// DeleteMood 删除心情记录
func (h *MoodHandler) DeleteMood(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
		return
	}

	// 获取entryId参数
	entryID := c.Param("entryId")
	if entryID == "" {
		response.BadRequest(c, "缺少心情记录ID")
		return
	}

	// 删除心情记录
	err := h.moodService.DeleteMood(userID.(string), entryID)
	if err != nil {
		logrus.Errorf("Delete mood failed: %v", err)
		if strings.Contains(err.Error(), "not found") {
			response.NotFound(c, "心情记录不存在")
			return
		}
		if strings.Contains(err.Error(), "permission denied") {
			response.Forbidden(c, "只能删除自己的心情记录")
			return
		}
		response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
		return
	}

	logrus.Infof("Mood deleted successfully: %s by user: %s", entryID, userID)
	response.Success(c, nil, "心情记录删除成功")
}

// GetCoupleMoods 获取情侣双方心情记录
func (h *MoodHandler) GetCoupleMoods(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
		return
	}

	// 解析查询参数
	var params models.MoodQueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		logrus.Warnf("Invalid query params: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 设置默认值
	if params.Limit <= 0 {
		params.Limit = 100
	}
	if params.Limit > 1000 {
		params.Limit = 1000 // 最大限制
	}

	// 获取情侣心情记录
	moods, err := h.moodService.GetCoupleMoods(userID.(string), &params)
	if err != nil {
		logrus.Errorf("Get couple moods failed: %v", err)
		if strings.Contains(err.Error(), "must have a couple relationship") {
			response.BadRequest(c, "必须先建立情侣关系才能查看心情记录")
			return
		}
		response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
		return
	}

	// 转换为响应格式
	var moodResponses []*models.MoodResponse
	for _, mood := range moods {
		moodResponses = append(moodResponses, mood.ToResponse())
	}

	logrus.Infof("Retrieved %d couple moods for user: %s", len(moodResponses), userID)
	response.Success(c, moodResponses, "获取情侣心情记录成功")
}
