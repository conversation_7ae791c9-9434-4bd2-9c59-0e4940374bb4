package handlers

import (
	"fmt"
	"strings"
	"time"

	"moodtracker-api/internal/models"
	"moodtracker-api/internal/services"
	"moodtracker-api/pkg/response"
	"moodtracker-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

// SyncHandler 同步处理器
type SyncHandler struct {
	syncService *services.SyncService
	validator   *validator.Validate
}

// NewSyncHandler 创建同步处理器
func NewSyncHandler(syncService *services.SyncService) *SyncHandler {
	return &SyncHandler{
		syncService: syncService,
		validator:   validator.New(),
	}
}

// GetChanges 获取增量变更
func (h *SyncHandler) GetChanges(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
		return
	}

	// 解析查询参数
	var req models.SyncChangesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logrus.Warnf("Invalid sync changes request: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 解析最后同步时间
	var lastSyncTime *time.Time
	if req.LastSyncTime != nil && *req.LastSyncTime != "" {
		parsedTime, err := time.Parse(time.RFC3339, *req.LastSyncTime)
		if err != nil {
			logrus.Warnf("Invalid lastSyncTime format: %v", err)
			response.BadRequest(c, "最后同步时间格式无效，请使用RFC3339格式")
			return
		}
		lastSyncTime = &parsedTime
	}

	// 获取变更
	changes, err := h.syncService.GetChanges(userID.(string), lastSyncTime)
	if err != nil {
		logrus.Errorf("Get changes failed: %v", err)
		if strings.Contains(err.Error(), "must have a couple relationship") {
			response.BadRequest(c, "必须先建立情侣关系才能同步数据")
			return
		}
		response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
		return
	}

	logrus.Infof("Retrieved %d changes for user: %s", len(changes.Changes), userID)
	response.Success(c, changes, "获取增量变更成功")
}

// UploadChanges 批量上传变更
func (h *SyncHandler) UploadChanges(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
		return
	}

	var req models.SyncUploadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.Warnf("Invalid upload changes request: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 验证请求参数
	if err := h.validator.Struct(&req); err != nil {
		logrus.Warnf("Upload changes validation failed: %v", err)
		response.BadRequest(c, utils.GetErrorMessage(utils.ErrInvalidRequest))
		return
	}

	// 验证变更数据
	for i, change := range req.Changes {
		if err := h.validator.Struct(change); err != nil {
			logrus.Warnf("Change validation failed at index %d: %v", i, err)
			response.BadRequest(c, fmt.Sprintf("第%d个变更数据格式无效", i+1))
			return
		}

		// 验证心情值范围
		if change.Mood < 1 || change.Mood > 12 {
			response.BadRequest(c, fmt.Sprintf("第%d个变更的心情值无效", i+1))
			return
		}
	}

	// 上传变更
	result, err := h.syncService.UploadChanges(userID.(string), &req)
	if err != nil {
		logrus.Errorf("Upload changes failed: %v", err)
		if strings.Contains(err.Error(), "must have a couple relationship") {
			response.BadRequest(c, "必须先建立情侣关系才能同步数据")
			return
		}
		response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
		return
	}

	logrus.Infof("Uploaded %d changes for user: %s (processed: %d, conflicts: %d, errors: %d)",
		len(req.Changes), userID, result.Processed, len(result.Conflicts), len(result.Errors))

	// 根据结果返回不同的响应
	if len(result.Errors) > 0 {
		response.Success(c, result, "部分变更上传成功，存在错误")
	} else if len(result.Conflicts) > 0 {
		response.Success(c, result, "变更上传成功，存在冲突需要处理")
	} else {
		response.Success(c, result, "所有变更上传成功")
	}
}

// GetLastSyncTime 获取最后同步时间
func (h *SyncHandler) GetLastSyncTime(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, utils.GetErrorMessage(utils.ErrUnauthorized))
		return
	}

	// 获取最后同步时间
	lastSyncTime, err := h.syncService.GetLastSyncTime(userID.(string))
	if err != nil {
		logrus.Errorf("Get last sync time failed: %v", err)
		if strings.Contains(err.Error(), "must have a couple relationship") {
			response.BadRequest(c, "必须先建立情侣关系")
			return
		}
		response.InternalServerError(c, utils.GetErrorMessage(utils.ErrInternalServer))
		return
	}

	var result map[string]interface{}
	if lastSyncTime != nil {
		result = map[string]interface{}{
			"lastSyncTime": lastSyncTime.Format(time.RFC3339),
		}
	} else {
		result = map[string]interface{}{
			"lastSyncTime": nil,
		}
	}

	response.Success(c, result, "获取最后同步时间成功")
}
