package services

import (
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"moodtracker-api/internal/database"
	"moodtracker-api/internal/models"
)

// SyncService 同步服务
type SyncService struct {
	db            *database.DB
	coupleService *CoupleService
	moodService   *MoodService
}

// NewSyncService 创建同步服务
func NewSyncService(db *database.DB, coupleService *CoupleService, moodService *MoodService) *SyncService {
	return &SyncService{
		db:            db,
		coupleService: coupleService,
		moodService:   moodService,
	}
}

// GetChanges 获取增量变更
func (s *SyncService) GetChanges(userID string, lastSyncTime *time.Time) (*models.SyncChangesResponse, error) {
	// 验证用户是否有情侣关系
	couple, err := s.coupleService.GetCoupleByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("user must have a couple relationship to sync data")
	}

	// 设置默认的最后同步时间（如果没有提供）
	var syncTime time.Time
	if lastSyncTime != nil {
		syncTime = *lastSyncTime
	} else {
		// 如果没有提供时间，返回最近30天的数据
		syncTime = time.Now().AddDate(0, 0, -30)
	}

	// 查询变更的心情记录
	query := `
		SELECT entry_id, user_id, couple_id, date, mood, note, created_at, updated_at, sync_status, is_deleted
		FROM moods 
		WHERE couple_id = ? AND updated_at > ?
		ORDER BY updated_at ASC
	`

	rows, err := s.db.Query(query, couple.CoupleID, syncTime)
	if err != nil {
		return nil, fmt.Errorf("failed to query changes: %w", err)
	}
	defer rows.Close()

	var changes []*models.MoodResponse
	for rows.Next() {
		mood := &models.Mood{}
		err := rows.Scan(
			&mood.EntryID, &mood.UserID, &mood.CoupleID, &mood.Date,
			&mood.MoodValue, &mood.Note, &mood.CreatedAt, &mood.UpdatedAt,
			&mood.SyncStatus, &mood.IsDeleted,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan mood: %w", err)
		}
		changes = append(changes, mood.ToResponse())
	}

	response := &models.SyncChangesResponse{
		Changes:      changes,
		LastSyncTime: time.Now(),
	}

	logrus.Infof("Retrieved %d changes for user %s since %v", len(changes), userID, syncTime)
	return response, nil
}

// UploadChanges 批量上传变更
func (s *SyncService) UploadChanges(userID string, req *models.SyncUploadRequest) (*models.SyncUploadResponse, error) {
	// 验证用户是否有情侣关系
	couple, err := s.coupleService.GetCoupleByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("user must have a couple relationship to sync data")
	}

	response := &models.SyncUploadResponse{
		Processed: 0,
		Conflicts: []*models.SyncConflictData{},
		Errors:    []*models.SyncErrorData{},
	}

	// 处理每个变更
	for _, change := range req.Changes {
		err := s.processChange(userID, couple.CoupleID, change, response)
		if err != nil {
			logrus.Errorf("Failed to process change %s: %v", change.EntryID, err)
			response.Errors = append(response.Errors, &models.SyncErrorData{
				EntryID: change.EntryID,
				Error:   err.Error(),
			})
		} else {
			response.Processed++
		}
	}

	logrus.Infof("Processed %d/%d changes for user %s", response.Processed, len(req.Changes), userID)
	return response, nil
}

// processChange 处理单个变更
func (s *SyncService) processChange(userID, coupleID string, change *models.SyncMoodData, response *models.SyncUploadResponse) error {
	// 解析日期
	date, err := time.Parse("2006-01-02", change.Date)
	if err != nil {
		return fmt.Errorf("invalid date format: %w", err)
	}

	// 检查是否已存在记录
	existingMood, err := s.moodService.GetMoodByID(change.EntryID)
	if err != nil && err.Error() != "mood not found" {
		return fmt.Errorf("failed to check existing mood: %w", err)
	}

	if existingMood != nil {
		// 记录已存在，检查冲突
		if existingMood.UpdatedAt.After(change.UpdatedAt) {
			// 服务器版本更新，产生冲突
			response.Conflicts = append(response.Conflicts, &models.SyncConflictData{
				EntryID:       change.EntryID,
				ConflictType:  "server_newer",
				ServerVersion: existingMood.UpdatedAt,
				ClientVersion: change.UpdatedAt,
			})
			return nil // 不是错误，只是冲突
		}

		// 客户端版本更新或相同，更新记录
		return s.updateExistingMood(existingMood, change)
	} else {
		// 记录不存在，创建新记录
		return s.createNewMood(userID, coupleID, change, date)
	}
}

// updateExistingMood 更新现有心情记录
func (s *SyncService) updateExistingMood(existing *models.Mood, change *models.SyncMoodData) error {
	query := `
		UPDATE moods 
		SET mood = ?, note = ?, updated_at = ?, is_deleted = ?
		WHERE entry_id = ?
	`

	_, err := s.db.Exec(query, change.Mood, change.Note, change.UpdatedAt, change.IsDeleted, change.EntryID)
	if err != nil {
		return fmt.Errorf("failed to update mood: %w", err)
	}

	return nil
}

// createNewMood 创建新的心情记录
func (s *SyncService) createNewMood(userID, coupleID string, change *models.SyncMoodData, date time.Time) error {
	// 检查当天是否已有其他记录（防止重复）
	existingByDate, err := s.moodService.GetMoodByUserAndDate(userID, date)
	if err == nil && existingByDate != nil && existingByDate.EntryID != change.EntryID {
		return fmt.Errorf("mood entry already exists for this date")
	}

	query := `
		INSERT INTO moods (entry_id, user_id, couple_id, date, mood, note, created_at, updated_at, sync_status, is_deleted)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err = s.db.Exec(query, change.EntryID, userID, coupleID, date,
		change.Mood, change.Note, change.CreatedAt, change.UpdatedAt, models.SyncStatusSynced, change.IsDeleted)
	if err != nil {
		return fmt.Errorf("failed to create mood: %w", err)
	}

	return nil
}

// GetLastSyncTime 获取用户最后同步时间
func (s *SyncService) GetLastSyncTime(userID string) (*time.Time, error) {
	// 验证用户是否有情侣关系
	couple, err := s.coupleService.GetCoupleByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("user must have a couple relationship")
	}

	var lastSyncTime time.Time
	query := `
		SELECT MAX(updated_at) 
		FROM moods 
		WHERE couple_id = ?
	`

	err = s.db.QueryRow(query, couple.CoupleID).Scan(&lastSyncTime)
	if err != nil {
		// 如果没有记录，返回nil
		return nil, nil
	}

	return &lastSyncTime, nil
}

// MarkSyncStatus 标记同步状态
func (s *SyncService) MarkSyncStatus(entryIDs []string, status models.SyncStatus) error {
	if len(entryIDs) == 0 {
		return nil
	}

	// 构建IN子句
	placeholders := ""
	args := []interface{}{}
	for i, entryID := range entryIDs {
		if i > 0 {
			placeholders += ", "
		}
		placeholders += "?"
		args = append(args, entryID)
	}

	query := fmt.Sprintf(`
		UPDATE moods 
		SET sync_status = ? 
		WHERE entry_id IN (%s)
	`, placeholders)

	args = append([]interface{}{status}, args...)

	_, err := s.db.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("failed to update sync status: %w", err)
	}

	logrus.Infof("Updated sync status to %s for %d entries", status, len(entryIDs))
	return nil
}
