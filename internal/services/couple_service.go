package services

import (
	"database/sql"
	"fmt"
	"time"

	"moodtracker-api/internal/database"
	"moodtracker-api/internal/models"
	"moodtracker-api/pkg/utils"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// CoupleService 情侣服务
type CoupleService struct {
	db          *database.DB
	userService *UserService
}

// NewCoupleService 创建情侣服务
func NewCoupleService(db *database.DB, userService *UserService) *CoupleService {
	return &CoupleService{
		db:          db,
		userService: userService,
	}
}

// CreateCouple 创建情侣关系
func (s *CoupleService) CreateCouple(userID string, req *models.CoupleCreateRequest) (*models.Couple, error) {
	// 检查用户是否已有情侣关系
	existingCouple, err := s.GetCoupleByUserID(userID)
	if err == nil && existingCouple != nil {
		return nil, fmt.Errorf("user already has a couple relationship")
	}

	// 生成匹配码
	matchCode, err := utils.GenerateMatchCode()
	if err != nil {
		return nil, fmt.Errorf("failed to generate match code: %w", err)
	}

	// 创建情侣关系
	couple := &models.Couple{
		CoupleID:         uuid.New().String(),
		User1ID:          userID,
		User2ID:          nil,
		MatchCode:        &matchCode,
		RelationshipName: &req.RelationshipName,
		Status:           models.CoupleStatusWaiting,
		CreatedAt:        time.Now(),
		MatchedAt:        nil,
	}

	// 插入数据库 - 包含expires_at字段（1小时后过期）
	expiresAt := time.Now().Add(1 * time.Hour)
	query := `
		INSERT INTO couples (couple_id, user1_id, match_code, relationship_name, status, created_at, expires_at)
		VALUES (?, ?, ?, ?, ?, ?, ?)
	`

	_, err = s.db.Exec(query, couple.CoupleID, couple.User1ID, couple.MatchCode,
		couple.RelationshipName, couple.Status, couple.CreatedAt, expiresAt)
	if err != nil {
		return nil, fmt.Errorf("failed to create couple: %w", err)
	}

	// 获取用户信息
	user1, err := s.userService.GetUserByID(userID)
	if err != nil {
		logrus.Warnf("Failed to get user info for couple response: %v", err)
	} else {
		couple.User1 = user1.ToResponse()
	}

	logrus.Infof("Couple relationship created: %s with match code: %s", couple.CoupleID, matchCode)
	return couple, nil
}

// JoinCouple 加入情侣关系
func (s *CoupleService) JoinCouple(userID string, req *models.CoupleJoinRequest) (*models.Couple, error) {
	// 检查用户是否已有情侣关系
	existingCouple, err := s.GetCoupleByUserID(userID)
	if err == nil && existingCouple != nil {
		return nil, fmt.Errorf("user already has a couple relationship")
	}

	// 查找匹配码对应的情侣关系
	couple, err := s.GetCoupleByMatchCode(req.MatchCode)
	if err != nil {
		return nil, fmt.Errorf("invalid match code")
	}

	// 检查状态
	if couple.Status != models.CoupleStatusWaiting {
		return nil, fmt.Errorf("couple relationship is not waiting for match")
	}

	// 检查匹配码是否过期（需要从数据库获取过期时间）
	var expiresAt time.Time
	expQuery := "SELECT expires_at FROM couples WHERE couple_id = ?"
	err = s.db.QueryRow(expQuery, couple.CoupleID).Scan(&expiresAt)
	if err != nil {
		return nil, fmt.Errorf("failed to check code expiration: %w", err)
	}

	if time.Now().After(expiresAt) {
		return nil, fmt.Errorf("match code has expired")
	}

	// 检查是否是同一个用户
	if couple.User1ID == userID {
		return nil, fmt.Errorf("cannot join your own couple relationship")
	}

	// 更新情侣关系
	now := time.Now()
	query := `
		UPDATE couples
		SET user2_id = ?, status = ?, matched_at = ?, match_code = ''
		WHERE couple_id = ?
	`

	_, err = s.db.Exec(query, userID, models.CoupleStatusMatched, now, couple.CoupleID)
	if err != nil {
		return nil, fmt.Errorf("failed to join couple: %w", err)
	}

	// 更新couple对象
	couple.User2ID = &userID
	couple.Status = models.CoupleStatusMatched
	couple.MatchedAt = &now
	emptyCode := ""
	couple.MatchCode = &emptyCode

	// 获取用户信息
	user1, err := s.userService.GetUserByID(couple.User1ID)
	if err != nil {
		logrus.Warnf("Failed to get user1 info: %v", err)
	} else {
		couple.User1 = user1.ToResponse()
	}

	user2, err := s.userService.GetUserByID(userID)
	if err != nil {
		logrus.Warnf("Failed to get user2 info: %v", err)
	} else {
		couple.User2 = user2.ToResponse()
	}

	logrus.Infof("User %s joined couple %s", userID, couple.CoupleID)
	return couple, nil
}

// GetCoupleByUserID 根据用户ID获取情侣关系
func (s *CoupleService) GetCoupleByUserID(userID string) (*models.Couple, error) {
	couple := &models.Couple{}
	query := `
		SELECT couple_id, user1_id, user2_id, match_code, relationship_name, 
			   status, created_at, matched_at
		FROM couples 
		WHERE user1_id = ? OR user2_id = ?
	`

	err := s.db.QueryRow(query, userID, userID).Scan(
		&couple.CoupleID, &couple.User1ID, &couple.User2ID, &couple.MatchCode,
		&couple.RelationshipName, &couple.Status, &couple.CreatedAt, &couple.MatchedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("couple not found")
		}
		return nil, fmt.Errorf("failed to get couple: %w", err)
	}

	// 获取用户信息
	user1, err := s.userService.GetUserByID(couple.User1ID)
	if err != nil {
		logrus.Warnf("Failed to get user1 info: %v", err)
	} else {
		couple.User1 = user1.ToResponse()
	}

	if couple.User2ID != nil {
		user2, err := s.userService.GetUserByID(*couple.User2ID)
		if err != nil {
			logrus.Warnf("Failed to get user2 info: %v", err)
		} else {
			couple.User2 = user2.ToResponse()
		}
	}

	return couple, nil
}

// GetCoupleByMatchCode 根据匹配码获取情侣关系
func (s *CoupleService) GetCoupleByMatchCode(matchCode string) (*models.Couple, error) {
	couple := &models.Couple{}
	query := `
		SELECT couple_id, user1_id, user2_id, match_code, relationship_name, 
			   status, created_at, matched_at
		FROM couples 
		WHERE match_code = ?
	`

	err := s.db.QueryRow(query, matchCode).Scan(
		&couple.CoupleID, &couple.User1ID, &couple.User2ID, &couple.MatchCode,
		&couple.RelationshipName, &couple.Status, &couple.CreatedAt, &couple.MatchedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("couple not found")
		}
		return nil, fmt.Errorf("failed to get couple: %w", err)
	}

	return couple, nil
}

// LeaveCouple 离开情侣关系
func (s *CoupleService) LeaveCouple(userID string) error {
	// 获取用户的情侣关系
	couple, err := s.GetCoupleByUserID(userID)
	if err != nil {
		return fmt.Errorf("no couple relationship found")
	}

	// 删除情侣关系
	query := "DELETE FROM couples WHERE couple_id = ?"
	_, err = s.db.Exec(query, couple.CoupleID)
	if err != nil {
		return fmt.Errorf("failed to delete couple: %w", err)
	}

	// TODO: 这里应该同时删除或标记相关的心情记录
	// 暂时跳过，在心情系统实现时处理

	logrus.Infof("User %s left couple %s", userID, couple.CoupleID)
	return nil
}

// GetCoupleInfo 获取情侣关系信息（包含用户信息）
func (s *CoupleService) GetCoupleInfo(userID string) (*models.Couple, error) {
	return s.GetCoupleByUserID(userID)
}
