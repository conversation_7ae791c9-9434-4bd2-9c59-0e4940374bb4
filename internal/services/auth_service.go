package services

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"moodtracker-api/internal/database"
	"moodtracker-api/internal/models"
	"moodtracker-api/pkg/utils"
)

// AuthService 认证服务
type AuthService struct {
	db         *database.DB
	jwtManager *utils.JWTManager
	userService *UserService
}

// NewAuthService 创建认证服务
func NewAuthService(db *database.DB, jwtManager *utils.JWTManager, userService *UserService) *AuthService {
	return &AuthService{
		db:          db,
		jwtManager:  jwtManager,
		userService: userService,
	}
}

// Register 用户注册
func (s *AuthService) Register(req *models.UserRegisterRequest) (*models.AuthResponse, error) {
	// 创建用户
	user, err := s.userService.CreateUser(req)
	if err != nil {
		return nil, err
	}

	// 生成设备ID（注册时可以为空，登录时提供）
	deviceID := uuid.New().String()

	// 生成访问令牌
	accessToken, expiresAt, err := s.jwtManager.GenerateToken(user.UserID, user.Username, deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	// 生成刷新令牌
	refreshToken, err := s.jwtManager.GenerateRefreshToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	// 保存刷新令牌
	err = s.SaveRefreshToken(user.UserID, refreshToken, &deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to save refresh token: %w", err)
	}

	return &models.AuthResponse{
		User:         user.ToResponse(),
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    expiresAt,
	}, nil
}

// Login 用户登录
func (s *AuthService) Login(req *models.UserLoginRequest) (*models.AuthResponse, error) {
	// 验证用户凭据
	user, err := s.userService.ValidateCredentials(req.Username, req.Password)
	if err != nil {
		return nil, fmt.Errorf("invalid credentials")
	}

	// 更新最后登录时间
	err = s.userService.UpdateLastLogin(user.UserID)
	if err != nil {
		logrus.Warnf("Failed to update last login for user %s: %v", user.UserID, err)
	}

	// 生成访问令牌
	accessToken, expiresAt, err := s.jwtManager.GenerateToken(user.UserID, user.Username, req.DeviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	// 生成刷新令牌
	refreshToken, err := s.jwtManager.GenerateRefreshToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	// 保存刷新令牌
	err = s.SaveRefreshToken(user.UserID, refreshToken, &req.DeviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to save refresh token: %w", err)
	}

	// 更新最后登录时间
	user.LastLoginAt = &time.Time{}
	*user.LastLoginAt = time.Now()

	return &models.AuthResponse{
		User:         user.ToResponse(),
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    expiresAt,
	}, nil
}

// RefreshToken 刷新访问令牌
func (s *AuthService) RefreshToken(refreshToken string) (*models.RefreshTokenResponse, error) {
	// 验证刷新令牌
	tokenRecord, err := s.GetRefreshToken(refreshToken)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token")
	}

	// 检查是否过期
	if time.Now().After(tokenRecord.ExpiresAt) {
		// 删除过期的令牌
		s.DeleteRefreshToken(refreshToken)
		return nil, fmt.Errorf("refresh token expired")
	}

	// 获取用户信息
	user, err := s.userService.GetUserByID(tokenRecord.UserID)
	if err != nil {
		return nil, fmt.Errorf("user not found")
	}

	// 生成新的访问令牌
	deviceID := ""
	if tokenRecord.DeviceID != nil {
		deviceID = *tokenRecord.DeviceID
	}
	
	accessToken, expiresAt, err := s.jwtManager.GenerateToken(user.UserID, user.Username, deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	// 生成新的刷新令牌
	newRefreshToken, err := s.jwtManager.GenerateRefreshToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	// 删除旧的刷新令牌
	err = s.DeleteRefreshToken(refreshToken)
	if err != nil {
		logrus.Warnf("Failed to delete old refresh token: %v", err)
	}

	// 保存新的刷新令牌
	err = s.SaveRefreshToken(user.UserID, newRefreshToken, tokenRecord.DeviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to save refresh token: %w", err)
	}

	return &models.RefreshTokenResponse{
		AccessToken:  accessToken,
		RefreshToken: newRefreshToken,
		ExpiresAt:    expiresAt,
	}, nil
}

// Logout 用户登出
func (s *AuthService) Logout(refreshToken string) error {
	// 删除刷新令牌
	err := s.DeleteRefreshToken(refreshToken)
	if err != nil {
		logrus.Warnf("Failed to delete refresh token during logout: %v", err)
	}
	return nil
}

// SaveRefreshToken 保存刷新令牌
func (s *AuthService) SaveRefreshToken(userID, token string, deviceID *string) error {
	tokenHash := utils.HashToken(token)
	expiresAt := time.Now().Add(30 * 24 * time.Hour) // 30天过期

	query := `
		INSERT INTO refresh_tokens (token_id, user_id, token_hash, device_id, expires_at)
		VALUES (?, ?, ?, ?, ?)
	`
	
	_, err := s.db.Exec(query, uuid.New().String(), userID, tokenHash, deviceID, expiresAt)
	if err != nil {
		return fmt.Errorf("failed to save refresh token: %w", err)
	}

	return nil
}

// GetRefreshToken 获取刷新令牌
func (s *AuthService) GetRefreshToken(token string) (*models.RefreshToken, error) {
	tokenHash := utils.HashToken(token)
	
	refreshToken := &models.RefreshToken{}
	query := `
		SELECT token_id, user_id, token_hash, device_id, expires_at, created_at
		FROM refresh_tokens 
		WHERE token_hash = ?
	`
	
	err := s.db.QueryRow(query, tokenHash).Scan(
		&refreshToken.TokenID, &refreshToken.UserID, &refreshToken.TokenHash,
		&refreshToken.DeviceID, &refreshToken.ExpiresAt, &refreshToken.CreatedAt,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("refresh token not found")
		}
		return nil, fmt.Errorf("failed to get refresh token: %w", err)
	}

	return refreshToken, nil
}

// DeleteRefreshToken 删除刷新令牌
func (s *AuthService) DeleteRefreshToken(token string) error {
	tokenHash := utils.HashToken(token)
	
	query := "DELETE FROM refresh_tokens WHERE token_hash = ?"
	_, err := s.db.Exec(query, tokenHash)
	if err != nil {
		return fmt.Errorf("failed to delete refresh token: %w", err)
	}

	return nil
}

// ValidateAccessToken 验证访问令牌
func (s *AuthService) ValidateAccessToken(token string) (*models.JWTClaims, error) {
	return s.jwtManager.ValidateToken(token)
}
