package services

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"moodtracker-api/internal/database"
	"moodtracker-api/internal/models"
	"moodtracker-api/pkg/utils"
)

// UserService 用户服务
type UserService struct {
	db *database.DB
}

// NewUserService 创建用户服务
func NewUserService(db *database.DB) *UserService {
	return &UserService{db: db}
}

// CreateUser 创建用户
func (s *UserService) CreateUser(req *models.UserRegisterRequest) (*models.User, error) {
	// 验证用户名格式
	if !utils.ValidateUsername(req.Username) {
		return nil, fmt.Errorf("invalid username format")
	}

	// 验证密码格式
	if !utils.ValidatePassword(req.Password) {
		return nil, fmt.Errorf("invalid password format")
	}

	// 检查用户名是否已存在
	exists, err := s.CheckUsernameExists(req.Username)
	if err != nil {
		return nil, fmt.Errorf("failed to check username: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("username already exists")
	}

	// 加密密码
	passwordHash, err := utils.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// 创建用户
	user := &models.User{
		UserID:       uuid.New().String(),
		Username:     req.Username,
		PasswordHash: passwordHash,
		Nickname:     req.Nickname,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// 插入数据库
	query := `
		INSERT INTO users (user_id, username, password_hash, nickname, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?)
	`
	
	_, err = s.db.Exec(query, user.UserID, user.Username, user.PasswordHash, 
		user.Nickname, user.CreatedAt, user.UpdatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	logrus.Infof("User created successfully: %s", user.Username)
	return user, nil
}

// GetUserByUsername 根据用户名获取用户
func (s *UserService) GetUserByUsername(username string) (*models.User, error) {
	user := &models.User{}
	query := `
		SELECT user_id, username, password_hash, nickname, avatar_url, 
			   created_at, updated_at, last_login_at
		FROM users 
		WHERE username = ?
	`
	
	err := s.db.QueryRow(query, username).Scan(
		&user.UserID, &user.Username, &user.PasswordHash,
		&user.Nickname, &user.AvatarURL, &user.CreatedAt,
		&user.UpdatedAt, &user.LastLoginAt,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return user, nil
}

// GetUserByID 根据用户ID获取用户
func (s *UserService) GetUserByID(userID string) (*models.User, error) {
	user := &models.User{}
	query := `
		SELECT user_id, username, password_hash, nickname, avatar_url, 
			   created_at, updated_at, last_login_at
		FROM users 
		WHERE user_id = ?
	`
	
	err := s.db.QueryRow(query, userID).Scan(
		&user.UserID, &user.Username, &user.PasswordHash,
		&user.Nickname, &user.AvatarURL, &user.CreatedAt,
		&user.UpdatedAt, &user.LastLoginAt,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return user, nil
}

// CheckUsernameExists 检查用户名是否存在
func (s *UserService) CheckUsernameExists(username string) (bool, error) {
	var count int
	query := "SELECT COUNT(*) FROM users WHERE username = ?"
	err := s.db.QueryRow(query, username).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("failed to check username: %w", err)
	}
	return count > 0, nil
}

// UpdateLastLogin 更新最后登录时间
func (s *UserService) UpdateLastLogin(userID string) error {
	query := "UPDATE users SET last_login_at = ? WHERE user_id = ?"
	_, err := s.db.Exec(query, time.Now(), userID)
	if err != nil {
		return fmt.Errorf("failed to update last login: %w", err)
	}
	return nil
}

// ValidateCredentials 验证用户凭据
func (s *UserService) ValidateCredentials(username, password string) (*models.User, error) {
	user, err := s.GetUserByUsername(username)
	if err != nil {
		return nil, err
	}

	if !utils.CheckPassword(password, user.PasswordHash) {
		return nil, fmt.Errorf("invalid credentials")
	}

	return user, nil
}
