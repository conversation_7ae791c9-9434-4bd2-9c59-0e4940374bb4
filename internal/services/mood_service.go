package services

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"moodtracker-api/internal/database"
	"moodtracker-api/internal/models"
)

// MoodService 心情服务
type MoodService struct {
	db            *database.DB
	coupleService *CoupleService
}

// NewMoodService 创建心情服务
func NewMoodService(db *database.DB, coupleService *CoupleService) *MoodService {
	return &MoodService{
		db:            db,
		coupleService: coupleService,
	}
}

// CreateMood 创建心情记录
func (s *MoodService) CreateMood(userID string, req *models.MoodCreateRequest) (*models.Mood, error) {
	// 验证用户是否有情侣关系
	couple, err := s.coupleService.GetCoupleByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("user must have a couple relationship to create mood")
	}

	// 解析日期
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %w", err)
	}

	// 检查当天是否已有心情记录
	existing, err := s.GetMoodByUserAndDate(userID, date)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("mood entry already exists for this date")
	}

	// 创建心情记录
	mood := &models.Mood{
		EntryID:    uuid.New().String(),
		UserID:     userID,
		CoupleID:   couple.CoupleID,
		Date:       date,
		MoodValue:  req.Mood,
		Note:       req.Note,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		SyncStatus: models.SyncStatusSynced,
		IsDeleted:  false,
	}

	// 插入数据库
	query := `
		INSERT INTO moods (entry_id, user_id, couple_id, date, mood, note, created_at, updated_at, sync_status, is_deleted)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err = s.db.Exec(query, mood.EntryID, mood.UserID, mood.CoupleID, mood.Date,
		mood.MoodValue, mood.Note, mood.CreatedAt, mood.UpdatedAt, mood.SyncStatus, mood.IsDeleted)
	if err != nil {
		return nil, fmt.Errorf("failed to create mood: %w", err)
	}

	logrus.Infof("Mood created successfully: %s for user %s", mood.EntryID, userID)
	return mood, nil
}

// UpdateMood 更新心情记录
func (s *MoodService) UpdateMood(userID, entryID string, req *models.MoodUpdateRequest) (*models.Mood, error) {
	// 获取现有记录
	mood, err := s.GetMoodByID(entryID)
	if err != nil {
		return nil, fmt.Errorf("mood entry not found")
	}

	// 验证所有权
	if mood.UserID != userID {
		return nil, fmt.Errorf("permission denied: can only update own mood entries")
	}

	// 检查是否已删除
	if mood.IsDeleted {
		return nil, fmt.Errorf("cannot update deleted mood entry")
	}

	// 更新字段
	mood.MoodValue = req.Mood
	mood.Note = req.Note
	mood.UpdatedAt = time.Now()

	// 更新数据库
	query := `
		UPDATE moods 
		SET mood = ?, note = ?, updated_at = ?
		WHERE entry_id = ?
	`

	_, err = s.db.Exec(query, mood.MoodValue, mood.Note, mood.UpdatedAt, mood.EntryID)
	if err != nil {
		return nil, fmt.Errorf("failed to update mood: %w", err)
	}

	logrus.Infof("Mood updated successfully: %s", entryID)
	return mood, nil
}

// DeleteMood 删除心情记录（软删除）
func (s *MoodService) DeleteMood(userID, entryID string) error {
	// 获取现有记录
	mood, err := s.GetMoodByID(entryID)
	if err != nil {
		return fmt.Errorf("mood entry not found")
	}

	// 验证所有权
	if mood.UserID != userID {
		return fmt.Errorf("permission denied: can only delete own mood entries")
	}

	// 软删除
	query := `
		UPDATE moods 
		SET is_deleted = TRUE, updated_at = ?
		WHERE entry_id = ?
	`

	_, err = s.db.Exec(query, time.Now(), entryID)
	if err != nil {
		return fmt.Errorf("failed to delete mood: %w", err)
	}

	logrus.Infof("Mood deleted successfully: %s", entryID)
	return nil
}

// GetMoodByID 根据ID获取心情记录
func (s *MoodService) GetMoodByID(entryID string) (*models.Mood, error) {
	mood := &models.Mood{}
	query := `
		SELECT entry_id, user_id, couple_id, date, mood, note, created_at, updated_at, sync_status, is_deleted
		FROM moods 
		WHERE entry_id = ?
	`

	err := s.db.QueryRow(query, entryID).Scan(
		&mood.EntryID, &mood.UserID, &mood.CoupleID, &mood.Date,
		&mood.MoodValue, &mood.Note, &mood.CreatedAt, &mood.UpdatedAt,
		&mood.SyncStatus, &mood.IsDeleted,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("mood not found")
		}
		return nil, fmt.Errorf("failed to get mood: %w", err)
	}

	return mood, nil
}

// GetMoodByUserAndDate 根据用户和日期获取心情记录
func (s *MoodService) GetMoodByUserAndDate(userID string, date time.Time) (*models.Mood, error) {
	mood := &models.Mood{}
	query := `
		SELECT entry_id, user_id, couple_id, date, mood, note, created_at, updated_at, sync_status, is_deleted
		FROM moods 
		WHERE user_id = ? AND date = ? AND is_deleted = FALSE
	`

	err := s.db.QueryRow(query, userID, date.Format("2006-01-02")).Scan(
		&mood.EntryID, &mood.UserID, &mood.CoupleID, &mood.Date,
		&mood.MoodValue, &mood.Note, &mood.CreatedAt, &mood.UpdatedAt,
		&mood.SyncStatus, &mood.IsDeleted,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("mood not found")
		}
		return nil, fmt.Errorf("failed to get mood: %w", err)
	}

	return mood, nil
}

// GetCoupleMoods 获取情侣双方的心情记录
func (s *MoodService) GetCoupleMoods(userID string, params *models.MoodQueryParams) ([]*models.Mood, error) {
	// 验证用户是否有情侣关系
	couple, err := s.coupleService.GetCoupleByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("user must have a couple relationship to view couple moods")
	}

	// 构建查询条件
	query := `
		SELECT entry_id, user_id, couple_id, date, mood, note, created_at, updated_at, sync_status, is_deleted
		FROM moods 
		WHERE couple_id = ? AND is_deleted = FALSE
	`
	args := []interface{}{couple.CoupleID}

	// 添加日期范围条件
	if params.StartDate != nil {
		query += " AND date >= ?"
		args = append(args, *params.StartDate)
	}
	if params.EndDate != nil {
		query += " AND date <= ?"
		args = append(args, *params.EndDate)
	}

	// 排序
	query += " ORDER BY date DESC, created_at DESC"

	// 分页
	if params.Limit > 0 {
		query += " LIMIT ?"
		args = append(args, params.Limit)
	} else {
		query += " LIMIT 100" // 默认限制
	}

	if params.Offset > 0 {
		query += " OFFSET ?"
		args = append(args, params.Offset)
	}

	// 执行查询
	rows, err := s.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query couple moods: %w", err)
	}
	defer rows.Close()

	var moods []*models.Mood
	for rows.Next() {
		mood := &models.Mood{}
		err := rows.Scan(
			&mood.EntryID, &mood.UserID, &mood.CoupleID, &mood.Date,
			&mood.MoodValue, &mood.Note, &mood.CreatedAt, &mood.UpdatedAt,
			&mood.SyncStatus, &mood.IsDeleted,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan mood: %w", err)
		}
		moods = append(moods, mood)
	}

	return moods, nil
}
