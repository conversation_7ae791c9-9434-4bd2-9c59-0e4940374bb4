package tests

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"moodtracker-api/config"
	"moodtracker-api/internal/database"
	"moodtracker-api/internal/handlers"
	"moodtracker-api/internal/middleware"
	"moodtracker-api/internal/services"
	"moodtracker-api/pkg/response"
	"moodtracker-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
)

// APITestSuite API测试套件
type APITestSuite struct {
	suite.Suite
	router      *gin.Engine
	db          *database.DB
	authService *services.AuthService

	// 测试数据
	testUser1 struct {
		Username     string
		Password     string
		Nickname     string
		DeviceID     string
		AccessToken  string
		RefreshToken string
		UserID       string
	}
	testUser2 struct {
		Username     string
		Password     string
		Nickname     string
		DeviceID     string
		AccessToken  string
		RefreshToken string
		UserID       string
	}
	testCouple struct {
		CoupleID         string
		MatchCode        string
		RelationshipName string
	}
	testMood struct {
		EntryID string
		Date    string
		Mood    int
		Note    string
	}
}

// APIResponse 通用API响应结构
type APIResponse struct {
	Success   bool        `json:"success"`
	Message   *string     `json:"message"`
	Data      interface{} `json:"data"`
	Error     *string     `json:"error"`
	Timestamp string      `json:"timestamp"`
}

// SetupSuite 测试套件初始化
func (suite *APITestSuite) SetupSuite() {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 加载测试配置
	cfg, err := config.LoadFromFile(".env.test")
	suite.Require().NoError(err)

	// 连接测试数据库
	suite.db, err = database.NewConnection(&cfg.Database)
	suite.Require().NoError(err)

	// 创建JWT管理器
	jwtManager := utils.NewJWTManager(cfg.JWT.Secret, cfg.JWT.ExpiresIn)

	// 创建服务
	userService := services.NewUserService(suite.db)
	suite.authService = services.NewAuthService(suite.db, jwtManager, userService)
	coupleService := services.NewCoupleService(suite.db, userService)
	moodService := services.NewMoodService(suite.db, coupleService)
	syncService := services.NewSyncService(suite.db, coupleService, moodService)

	// 创建处理器
	authHandler := handlers.NewAuthHandler(suite.authService)
	coupleHandler := handlers.NewCoupleHandler(coupleService)
	moodHandler := handlers.NewMoodHandler(moodService)
	syncHandler := handlers.NewSyncHandler(syncService)

	// 创建路由
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())

	// 健康检查
	suite.router.GET("/health", func(c *gin.Context) {
		response.Success(c, map[string]interface{}{
			"database": "connected",
			"status":   "ok",
		}, "服务运行正常")
	})

	// API路由组
	api := suite.router.Group("/api/v1")
	{
		// 认证路由（无需认证）
		auth := api.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
			auth.POST("/refresh-token", authHandler.RefreshToken)
			auth.POST("/logout", authHandler.Logout)
		}

		// 用户路由（需要认证）
		user := api.Group("/user")
		user.Use(middleware.AuthMiddleware(suite.authService))
		{
			user.GET("/me", authHandler.GetCurrentUser)
		}

		// 情侣路由（需要认证）
		couples := api.Group("/couples")
		couples.Use(middleware.AuthMiddleware(suite.authService))
		{
			couples.POST("/create", coupleHandler.CreateCouple)
			couples.POST("/join", coupleHandler.JoinCouple)
			couples.GET("/info", coupleHandler.GetCoupleInfo)
			couples.DELETE("/leave", coupleHandler.LeaveCouple)
		}

		// 心情路由（需要认证）
		moods := api.Group("/moods")
		moods.Use(middleware.AuthMiddleware(suite.authService))
		{
			moods.POST("", moodHandler.CreateMood)
			moods.PUT("/:entryId", moodHandler.UpdateMood)
			moods.DELETE("/:entryId", moodHandler.DeleteMood)
			moods.GET("/couple", moodHandler.GetCoupleMoods)
		}

		// 同步路由（需要认证）
		sync := api.Group("/sync")
		sync.Use(middleware.AuthMiddleware(suite.authService))
		{
			sync.GET("/changes", syncHandler.GetChanges)
			sync.POST("/upload", syncHandler.UploadChanges)
			sync.GET("/last-sync-time", syncHandler.GetLastSyncTime)
		}
	}

	// 清理数据库
	suite.cleanDatabase()

	// 初始化测试数据
	suite.initTestData()

	// 注册测试用户并获取令牌
	suite.setupTestUsers()
}

// TearDownSuite 测试套件清理
func (suite *APITestSuite) TearDownSuite() {
	if suite.db != nil {
		suite.db.Close()
	}
}

// initTestData 初始化测试数据
func (suite *APITestSuite) initTestData() {
	suite.testUser1.Username = "testuser1"
	suite.testUser1.Password = "password123"
	suite.testUser1.Nickname = "测试用户1"
	suite.testUser1.DeviceID = uuid.New().String()

	suite.testUser2.Username = "testuser2"
	suite.testUser2.Password = "password123"
	suite.testUser2.Nickname = "测试用户2"
	suite.testUser2.DeviceID = uuid.New().String()

	suite.testCouple.RelationshipName = "我们的测试关系"

	suite.testMood.Date = time.Now().Format("2006-01-02")
	suite.testMood.Mood = 8
	suite.testMood.Note = "今天心情很好！"
}

// cleanDatabase 清理数据库
func (suite *APITestSuite) cleanDatabase() {
	// 清理所有表的数据，保持外键约束
	tables := []string{"refresh_tokens", "moods", "couples", "users", "migrations"}

	for _, table := range tables {
		_, err := suite.db.Exec(fmt.Sprintf("DELETE FROM %s", table))
		if err != nil {
			// 忽略清理错误，继续执行
			fmt.Printf("Warning: Failed to clean table %s: %v\n", table, err)
		}
	}
}

// setupTestUsers 设置测试用户并获取访问令牌
func (suite *APITestSuite) setupTestUsers() {
	// 注册用户1
	reqBody1 := map[string]interface{}{
		"username": suite.testUser1.Username,
		"password": suite.testUser1.Password,
		"nickname": suite.testUser1.Nickname,
	}

	w1 := suite.makeRequest("POST", "/api/v1/auth/register", reqBody1, nil)
	suite.Require().Equal(http.StatusOK, w1.Code)

	resp1 := suite.parseResponse(w1)
	suite.Require().True(resp1.Success)
	suite.Require().NotNil(resp1.Data)

	data1, ok := resp1.Data.(map[string]interface{})
	suite.Require().True(ok)

	user1, ok := data1["user"].(map[string]interface{})
	suite.Require().True(ok)

	// 保存用户1的令牌和ID
	suite.testUser1.AccessToken = data1["accessToken"].(string)
	suite.testUser1.RefreshToken = data1["refreshToken"].(string)
	suite.testUser1.UserID = user1["userId"].(string)

	// 注册用户2
	reqBody2 := map[string]interface{}{
		"username": suite.testUser2.Username,
		"password": suite.testUser2.Password,
		"nickname": suite.testUser2.Nickname,
	}

	w2 := suite.makeRequest("POST", "/api/v1/auth/register", reqBody2, nil)
	suite.Require().Equal(http.StatusOK, w2.Code)

	resp2 := suite.parseResponse(w2)
	suite.Require().True(resp2.Success)
	suite.Require().NotNil(resp2.Data)

	data2, ok := resp2.Data.(map[string]interface{})
	suite.Require().True(ok)

	user2, ok := data2["user"].(map[string]interface{})
	suite.Require().True(ok)

	// 保存用户2的令牌和ID
	suite.testUser2.AccessToken = data2["accessToken"].(string)
	suite.testUser2.RefreshToken = data2["refreshToken"].(string)
	suite.testUser2.UserID = user2["userId"].(string)
}

// ensureCoupleMatched 确保情侣关系已匹配
func (suite *APITestSuite) ensureCoupleMatched() {
	// 检查用户1是否有情侣关系
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	w := suite.makeRequest("GET", "/api/v1/couples/info", nil, headers)
	if w.Code == http.StatusOK {
		resp := suite.parseResponse(w)
		if resp.Success && resp.Data != nil {
			data, ok := resp.Data.(map[string]interface{})
			if ok && data["status"] == "matched" {
				// 已经有匹配的情侣关系
				suite.testCouple.CoupleID = data["coupleId"].(string)
				return
			}
		}
	}

	// 没有情侣关系或未匹配，重新创建
	suite.createAndMatchCouple()
}

// createAndMatchCouple 创建并匹配情侣关系
func (suite *APITestSuite) createAndMatchCouple() {
	// 先清理现有关系
	suite.cleanCoupleRelationship()

	// 创建新的情侣关系
	suite.createCoupleRelationship()

	// 让用户2加入
	if suite.testCouple.MatchCode != "" {
		suite.joinCoupleRelationship()
	}
}

// createCoupleRelationship 创建情侣关系的辅助方法
func (suite *APITestSuite) createCoupleRelationship() {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	reqBody := map[string]interface{}{
		"relationshipName": "我们的测试关系",
	}

	w := suite.makeRequest("POST", "/api/v1/couples/create", reqBody, headers)
	if w.Code == http.StatusOK {
		resp := suite.parseResponse(w)
		if resp.Success && resp.Data != nil {
			data, ok := resp.Data.(map[string]interface{})
			if ok {
				suite.testCouple.CoupleID = data["coupleId"].(string)
				if matchCode, exists := data["matchCode"]; exists && matchCode != nil {
					suite.testCouple.MatchCode = matchCode.(string)
				}
			}
		}
	}
}

// joinCoupleRelationship 加入情侣关系的辅助方法
func (suite *APITestSuite) joinCoupleRelationship() {
	if suite.testCouple.MatchCode == "" {
		return
	}

	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser2.AccessToken,
	}

	reqBody := map[string]interface{}{
		"matchCode": suite.testCouple.MatchCode,
	}

	w := suite.makeRequest("POST", "/api/v1/couples/join", reqBody, headers)
	if w.Code == http.StatusOK {
		// 匹配成功，清空匹配码
		suite.testCouple.MatchCode = ""
	}
}

// createMoodForUpdate 为更新测试创建心情记录
func (suite *APITestSuite) createMoodForUpdate() {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	// 使用不同的日期避免冲突
	updateDate := time.Now().AddDate(0, 0, 1).Format("2006-01-02") // 明天的日期

	reqBody := map[string]interface{}{
		"date": updateDate,
		"mood": 8,
		"note": "准备更新的心情记录",
	}

	w := suite.makeRequest("POST", "/api/v1/moods", reqBody, headers)
	if w.Code == http.StatusOK {
		resp := suite.parseResponse(w)
		if resp.Success && resp.Data != nil {
			data, ok := resp.Data.(map[string]interface{})
			if ok {
				suite.testMood.EntryID = data["entryId"].(string)
			}
		}
	}
}

// createMoodForTesting 为测试创建心情记录
func (suite *APITestSuite) createMoodForTesting() {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	// 使用明天的日期避免与其他测试冲突
	testDate := time.Now().AddDate(0, 0, 2).Format("2006-01-02") // 后天的日期

	reqBody := map[string]interface{}{
		"date": testDate,
		"mood": 9,
		"note": "测试心情记录",
	}

	w := suite.makeRequest("POST", "/api/v1/moods", reqBody, headers)
	if w.Code == http.StatusOK {
		resp := suite.parseResponse(w)
		if resp.Success && resp.Data != nil {
			data, ok := resp.Data.(map[string]interface{})
			if ok && data["entryId"] != nil {
				// 保存entryId供其他测试使用
				suite.testMood.EntryID = data["entryId"].(string)
			}
		}
	}
}

// createNewCoupleForJoinTest 为加入测试创建新的情侣关系
func (suite *APITestSuite) createNewCoupleForJoinTest() {
	// 先清理现有的情侣关系
	suite.cleanCoupleRelationship()

	// 创建新的情侣关系
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	reqBody := map[string]interface{}{
		"relationshipName": "加入测试关系",
	}

	w := suite.makeRequest("POST", "/api/v1/couples/create", reqBody, headers)
	if w.Code == http.StatusOK {
		resp := suite.parseResponse(w)
		if resp.Success && resp.Data != nil {
			data, ok := resp.Data.(map[string]interface{})
			if ok {
				suite.testCouple.CoupleID = data["coupleId"].(string)
				if matchCode, exists := data["matchCode"]; exists && matchCode != nil {
					suite.testCouple.MatchCode = matchCode.(string)
				}
			}
		}
	}
}

// cleanCoupleRelationship 清理情侣关系
func (suite *APITestSuite) cleanCoupleRelationship() {
	// 尝试让用户1离开情侣关系
	headers1 := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}
	suite.makeRequest("DELETE", "/api/v1/couples/leave", nil, headers1)

	// 尝试让用户2离开情侣关系
	headers2 := map[string]string{
		"Authorization": "Bearer " + suite.testUser2.AccessToken,
	}
	suite.makeRequest("DELETE", "/api/v1/couples/leave", nil, headers2)

	// 重置测试数据
	suite.testCouple.CoupleID = ""
	suite.testCouple.MatchCode = ""
}

// makeRequest 发送HTTP请求的辅助方法
func (suite *APITestSuite) makeRequest(method, url string, body interface{}, headers map[string]string) *httptest.ResponseRecorder {
	var reqBody []byte
	if body != nil {
		var err error
		reqBody, err = json.Marshal(body)
		suite.Require().NoError(err)
	}

	req, err := http.NewRequest(method, url, bytes.NewBuffer(reqBody))
	suite.Require().NoError(err)

	// 设置默认头部
	req.Header.Set("Content-Type", "application/json")

	// 设置自定义头部
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	return w
}

// parseResponse 解析响应的辅助方法
func (suite *APITestSuite) parseResponse(w *httptest.ResponseRecorder) *APIResponse {
	var resp APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &resp)
	suite.Require().NoError(err)
	return &resp
}

// TestHealthCheck 测试健康检查
func (suite *APITestSuite) TestHealthCheck() {
	w := suite.makeRequest("GET", "/health", nil, nil)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.NotNil(resp.Data)
	suite.Nil(resp.Error)
	suite.NotEmpty(resp.Timestamp)

	// 验证响应数据结构
	data, ok := resp.Data.(map[string]interface{})
	suite.True(ok)
	suite.Equal("connected", data["database"])
	suite.Equal("ok", data["status"])
}

// TestUserRegistration 测试用户注册
func (suite *APITestSuite) TestUserRegistration() {
	// 验证用户1已经注册成功（在SetupSuite中完成）
	suite.NotEmpty(suite.testUser1.AccessToken)
	suite.NotEmpty(suite.testUser1.RefreshToken)
	suite.NotEmpty(suite.testUser1.UserID)

	// 测试重复注册应该失败
	reqBody := map[string]interface{}{
		"username": suite.testUser1.Username,
		"password": suite.testUser1.Password,
		"nickname": "重复用户",
	}

	w := suite.makeRequest("POST", "/api/v1/auth/register", reqBody, nil)

	// 应该返回400错误（用户名已存在）
	suite.Equal(http.StatusBadRequest, w.Code)

	resp := suite.parseResponse(w)
	suite.False(resp.Success)
	suite.NotNil(resp.Error)
}

// TestUserLogin 测试用户登录
func (suite *APITestSuite) TestUserLogin() {
	// 验证用户2已经注册成功（在SetupSuite中完成）
	suite.NotEmpty(suite.testUser2.AccessToken)
	suite.NotEmpty(suite.testUser2.RefreshToken)
	suite.NotEmpty(suite.testUser2.UserID)

	// 测试用户2重新登录
	reqBody := map[string]interface{}{
		"username": suite.testUser2.Username,
		"password": suite.testUser2.Password,
		"deviceId": suite.testUser2.DeviceID,
	}

	w := suite.makeRequest("POST", "/api/v1/auth/login", reqBody, nil)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.NotNil(resp.Data)

	// 验证响应数据结构
	data, ok := resp.Data.(map[string]interface{})
	suite.True(ok)

	user, ok := data["user"].(map[string]interface{})
	suite.True(ok)
	suite.Equal(suite.testUser2.Username, user["username"])
	suite.NotEmpty(data["accessToken"])
	suite.NotEmpty(data["refreshToken"])
}

// TestGetCurrentUser 测试获取当前用户信息
func (suite *APITestSuite) TestGetCurrentUser() {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	w := suite.makeRequest("GET", "/api/v1/user/me", nil, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.NotNil(resp.Data)

	// 验证响应数据结构（根据API文档，只返回userId和username）
	data, ok := resp.Data.(map[string]interface{})
	suite.True(ok)
	suite.Equal(suite.testUser1.UserID, data["userId"])
	suite.Equal(suite.testUser1.Username, data["username"])

	// 确保只有这两个字段
	suite.Len(data, 2)
}

// TestRefreshToken 测试刷新令牌
func (suite *APITestSuite) TestRefreshToken() {
	// 使用用户2的刷新令牌，避免与登出测试冲突
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser2.RefreshToken,
	}

	w := suite.makeRequest("POST", "/api/v1/auth/refresh-token", nil, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.NotNil(resp.Data)

	// 验证响应数据结构
	data, ok := resp.Data.(map[string]interface{})
	suite.True(ok)
	suite.NotEmpty(data["accessToken"])
	suite.NotEmpty(data["refreshToken"])
	suite.NotEmpty(data["expiresAt"])

	// 更新用户2的令牌
	suite.testUser2.AccessToken = data["accessToken"].(string)
	suite.testUser2.RefreshToken = data["refreshToken"].(string)
}

// TestCreateCouple 测试创建情侣关系
func (suite *APITestSuite) TestCreateCouple() {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	reqBody := map[string]interface{}{
		"relationshipName": suite.testCouple.RelationshipName,
	}

	w := suite.makeRequest("POST", "/api/v1/couples/create", reqBody, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.NotNil(resp.Data)

	// 验证响应数据结构
	data, ok := resp.Data.(map[string]interface{})
	suite.True(ok)

	suite.NotEmpty(data["coupleId"])
	suite.NotEmpty(data["matchCode"])
	suite.Equal(suite.testCouple.RelationshipName, data["relationshipName"])
	suite.Equal("waiting", data["status"])
	suite.NotEmpty(data["createdAt"])
	suite.Nil(data["matchedAt"])

	// 验证用户信息
	user1, ok := data["user1"].(map[string]interface{})
	suite.True(ok)
	suite.Equal(suite.testUser1.UserID, user1["userId"])
	suite.Equal(suite.testUser1.Username, user1["username"])
	suite.Equal(suite.testUser1.Nickname, user1["nickname"])
	// avatarUrl 和 lastLoginAt 可以为 nil
	suite.NotEmpty(user1["createdAt"])

	suite.Nil(data["user2"])

	// 保存测试数据
	suite.testCouple.CoupleID = data["coupleId"].(string)
	suite.testCouple.MatchCode = data["matchCode"].(string)
}

// TestJoinCouple 测试加入情侣关系
func (suite *APITestSuite) TestJoinCouple() {
	// 创建一个新的情侣关系用于测试加入功能
	suite.createNewCoupleForJoinTest()

	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser2.AccessToken,
	}

	reqBody := map[string]interface{}{
		"matchCode": suite.testCouple.MatchCode,
	}

	w := suite.makeRequest("POST", "/api/v1/couples/join", reqBody, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.NotNil(resp.Data)

	// 验证响应数据结构
	data, ok := resp.Data.(map[string]interface{})
	suite.True(ok)

	suite.Equal(suite.testCouple.CoupleID, data["coupleId"])
	suite.Equal("", data["matchCode"]) // 匹配后清空
	suite.Equal("matched", data["status"])
	suite.NotEmpty(data["matchedAt"])

	// 验证两个用户信息都存在
	user1, ok := data["user1"].(map[string]interface{})
	suite.True(ok)
	suite.Equal(suite.testUser1.UserID, user1["userId"])

	user2, ok := data["user2"].(map[string]interface{})
	suite.True(ok)
	suite.Equal(suite.testUser2.UserID, user2["userId"])
}

// TestGetCoupleInfo 测试获取情侣信息
func (suite *APITestSuite) TestGetCoupleInfo() {
	// 首先确保用户2加入了情侣关系
	suite.ensureCoupleMatched()

	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	w := suite.makeRequest("GET", "/api/v1/couples/info", nil, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.NotNil(resp.Data)

	// 验证响应数据结构
	data, ok := resp.Data.(map[string]interface{})
	suite.True(ok)

	suite.Equal(suite.testCouple.CoupleID, data["coupleId"])
	// 现在应该是 matched 状态
	suite.Equal("matched", data["status"])
	suite.NotNil(data["user1"])
	suite.NotNil(data["user2"])
}

// TestCreateMood 测试创建心情记录
func (suite *APITestSuite) TestCreateMood() {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	reqBody := map[string]interface{}{
		"date": suite.testMood.Date,
		"mood": suite.testMood.Mood,
		"note": suite.testMood.Note,
	}

	w := suite.makeRequest("POST", "/api/v1/moods", reqBody, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.NotNil(resp.Data)

	// 验证响应数据结构
	data, ok := resp.Data.(map[string]interface{})
	suite.True(ok)

	suite.NotEmpty(data["entryId"])
	suite.Equal(suite.testUser1.UserID, data["userId"])
	suite.Equal(suite.testCouple.CoupleID, data["coupleId"])
	suite.Equal(suite.testMood.Date, data["date"])
	suite.Equal(float64(suite.testMood.Mood), data["mood"]) // JSON数字解析为float64
	suite.Equal(suite.testMood.Note, data["note"])
	suite.NotEmpty(data["createdAt"])
	suite.NotEmpty(data["updatedAt"])
	suite.Equal("SYNCED", data["syncStatus"])
	suite.False(data["isDeleted"].(bool))

	// 保存entryId用于后续测试
	suite.testMood.EntryID = data["entryId"].(string)
}

// TestUpdateMood 测试更新心情记录
func (suite *APITestSuite) TestUpdateMood() {
	// 确保有情侣关系
	suite.ensureCoupleMatched()

	// 先创建一个新的心情记录用于更新测试
	suite.createMoodForUpdate()

	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	newMood := 10
	newNote := "心情更好了！"

	reqBody := map[string]interface{}{
		"mood": newMood,
		"note": newNote,
	}

	url := fmt.Sprintf("/api/v1/moods/%s", suite.testMood.EntryID)
	w := suite.makeRequest("PUT", url, reqBody, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.NotNil(resp.Data)

	// 验证响应数据结构
	data, ok := resp.Data.(map[string]interface{})
	suite.True(ok)

	suite.Equal(suite.testMood.EntryID, data["entryId"])
	suite.Equal(float64(newMood), data["mood"])
	suite.Equal(newNote, data["note"])
	suite.NotEmpty(data["updatedAt"])

	// 更新测试数据
	suite.testMood.Mood = newMood
	suite.testMood.Note = newNote
}

// TestGetCoupleMoods 测试获取情侣心情记录
func (suite *APITestSuite) TestGetCoupleMoods() {
	// 确保有情侣关系和心情记录
	suite.ensureCoupleMatched()
	suite.createMoodForTesting()

	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	w := suite.makeRequest("GET", "/api/v1/moods/couple", nil, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.NotNil(resp.Data)

	// 验证响应数据结构（应该是数组）
	data, ok := resp.Data.([]interface{})
	suite.True(ok)
	suite.GreaterOrEqual(len(data), 1) // 至少有一条记录

	// 验证第一条记录的结构
	if len(data) > 0 {
		mood, ok := data[0].(map[string]interface{})
		suite.True(ok)
		suite.NotEmpty(mood["entryId"])
		suite.NotEmpty(mood["userId"])
		suite.NotEmpty(mood["coupleId"])
		suite.NotEmpty(mood["date"])
		suite.NotNil(mood["mood"])
		suite.NotEmpty(mood["createdAt"])
		suite.NotEmpty(mood["updatedAt"])
		suite.NotNil(mood["syncStatus"])
		suite.NotNil(mood["isDeleted"])
	}
}

// TestGetCoupleMoodsWithParams 测试带参数获取情侣心情记录
func (suite *APITestSuite) TestGetCoupleMoodsWithParams() {
	// 确保有情侣关系和心情记录
	suite.ensureCoupleMatched()
	suite.createMoodForTesting()

	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	// 测试带查询参数
	url := "/api/v1/moods/couple?limit=10&offset=0"
	w := suite.makeRequest("GET", url, nil, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.NotNil(resp.Data)
}

// TestGetLastSyncTime 测试获取最后同步时间
func (suite *APITestSuite) TestGetLastSyncTime() {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	w := suite.makeRequest("GET", "/api/v1/sync/last-sync-time", nil, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.NotNil(resp.Data)

	// 验证响应数据结构
	data, ok := resp.Data.(map[string]interface{})
	suite.True(ok)
	suite.NotEmpty(data["lastSyncTime"])
}

// TestGetSyncChanges 测试获取同步变更
func (suite *APITestSuite) TestGetSyncChanges() {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	w := suite.makeRequest("GET", "/api/v1/sync/changes", nil, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.NotNil(resp.Data)

	// 验证响应数据结构
	data, ok := resp.Data.(map[string]interface{})
	suite.True(ok)
	suite.NotNil(data["changes"])
	suite.NotEmpty(data["lastSyncTime"])

	// 验证changes是数组
	changes, ok := data["changes"].([]interface{})
	suite.True(ok)
	suite.GreaterOrEqual(len(changes), 1) // 应该至少有一条变更
}

// TestUploadSyncChanges 测试上传同步变更
func (suite *APITestSuite) TestUploadSyncChanges() {
	// 确保有情侣关系
	suite.ensureCoupleMatched()

	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken, // 使用testUser1
	}

	// 创建一个新的心情记录用于同步上传
	newEntryID := uuid.New().String()
	// 使用一个唯一的日期避免冲突
	syncDate := time.Now().AddDate(0, 0, 10).Format("2006-01-02") // 10天后的日期

	reqBody := map[string]interface{}{
		"changes": []map[string]interface{}{
			{
				"entryId":   newEntryID,
				"date":      syncDate,
				"mood":      7,
				"note":      "同步测试心情",
				"createdAt": time.Now().Format(time.RFC3339),
				"updatedAt": time.Now().Format(time.RFC3339),
				"isDeleted": false,
			},
		},
	}

	w := suite.makeRequest("POST", "/api/v1/sync/upload", reqBody, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.NotNil(resp.Data)

	// 验证响应数据结构
	data, ok := resp.Data.(map[string]interface{})
	suite.True(ok)
	suite.Equal(float64(1), data["processed"]) // 处理了1条记录

	// 验证conflicts和errors是数组
	conflicts, ok := data["conflicts"].([]interface{})
	suite.True(ok)
	suite.Equal(0, len(conflicts))

	errors, ok := data["errors"].([]interface{})
	suite.True(ok)
	suite.Equal(0, len(errors))
}

// TestGetSyncChangesWithTime 测试带时间参数获取同步变更
func (suite *APITestSuite) TestGetSyncChangesWithTime() {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	// 使用一个很早的时间，确保能获取到所有变更
	lastSyncTime := "2025-01-01T00:00:00Z"
	url := fmt.Sprintf("/api/v1/sync/changes?lastSyncTime=%s", lastSyncTime)

	w := suite.makeRequest("GET", url, nil, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.NotNil(resp.Data)

	// 验证响应数据结构
	data, ok := resp.Data.(map[string]interface{})
	suite.True(ok)
	suite.NotNil(data["changes"])
	suite.NotEmpty(data["lastSyncTime"])
}

// TestDeleteMood 测试删除心情记录
func (suite *APITestSuite) TestDeleteMood() {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	url := fmt.Sprintf("/api/v1/moods/%s", suite.testMood.EntryID)
	w := suite.makeRequest("DELETE", url, nil, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.Nil(resp.Data) // 删除操作返回null
	suite.NotNil(resp.Message)
}

// TestLeaveCouple 测试离开情侣关系
func (suite *APITestSuite) TestLeaveCouple() {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.AccessToken,
	}

	w := suite.makeRequest("DELETE", "/api/v1/couples/leave", nil, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.Nil(resp.Data) // 离开操作返回null
	suite.NotNil(resp.Message)
}

// TestLogout 测试用户登出
func (suite *APITestSuite) TestLogout() {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser1.RefreshToken,
	}

	w := suite.makeRequest("POST", "/api/v1/auth/logout", nil, headers)

	suite.Equal(http.StatusOK, w.Code)

	resp := suite.parseResponse(w)
	suite.True(resp.Success)
	suite.Nil(resp.Data) // 登出操作返回null
	suite.NotNil(resp.Message)
}

// TestErrorCases 测试错误情况
func (suite *APITestSuite) TestErrorCases() {
	// 测试无效的认证令牌
	headers := map[string]string{
		"Authorization": "Bearer invalid-token",
	}

	w := suite.makeRequest("GET", "/api/v1/user/me", nil, headers)
	suite.Equal(http.StatusUnauthorized, w.Code)

	resp := suite.parseResponse(w)
	suite.False(resp.Success)
	suite.NotNil(resp.Error)

	// 测试缺少认证头
	w = suite.makeRequest("GET", "/api/v1/user/me", nil, nil)
	suite.Equal(http.StatusUnauthorized, w.Code)

	// 测试无效的请求体
	invalidBody := "invalid json"
	req, _ := http.NewRequest("POST", "/api/v1/auth/register", bytes.NewBufferString(invalidBody))
	req.Header.Set("Content-Type", "application/json")

	w = httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	suite.Equal(http.StatusBadRequest, w.Code)

	// 测试重复用户名注册
	reqBody := map[string]interface{}{
		"username": suite.testUser1.Username, // 使用已存在的用户名
		"password": "password123",
		"nickname": "重复用户",
	}

	w = suite.makeRequest("POST", "/api/v1/auth/register", reqBody, nil)
	suite.Equal(http.StatusBadRequest, w.Code)
}

// TestValidationErrors 测试参数验证错误
func (suite *APITestSuite) TestValidationErrors() {
	// 测试心情值超出范围
	headers := map[string]string{
		"Authorization": "Bearer " + suite.testUser2.AccessToken,
	}

	reqBody := map[string]interface{}{
		"date": "2025-08-05",
		"mood": 15, // 超出1-12范围
		"note": "无效心情值",
	}

	w := suite.makeRequest("POST", "/api/v1/moods", reqBody, headers)
	suite.Equal(http.StatusBadRequest, w.Code)

	resp := suite.parseResponse(w)
	suite.False(resp.Success)
	suite.NotNil(resp.Error)

	// 测试无效日期格式
	reqBody = map[string]interface{}{
		"date": "invalid-date",
		"mood": 8,
		"note": "无效日期",
	}

	w = suite.makeRequest("POST", "/api/v1/moods", reqBody, headers)
	suite.Equal(http.StatusBadRequest, w.Code)

	// 测试缺少必填字段
	reqBody = map[string]interface{}{
		"mood": 8,
		// 缺少date字段
	}

	w = suite.makeRequest("POST", "/api/v1/moods", reqBody, headers)
	suite.Equal(http.StatusBadRequest, w.Code)
}

// TestRunAPITests 运行API测试套件
func TestRunAPITests(t *testing.T) {
	suite.Run(t, new(APITestSuite))
}
