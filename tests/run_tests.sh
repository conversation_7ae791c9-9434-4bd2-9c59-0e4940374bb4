#!/bin/bash

# MoodTracker API 全覆盖测试脚本
# 根据API文档验证所有端点的请求方法和返回值

set -e

echo "🚀 MoodTracker API 全覆盖测试开始"
echo "=================================="

# 检查环境
echo "📋 检查测试环境..."

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go 未安装"
    exit 1
fi

echo "✅ Go 版本: $(go version)"

# 检查测试配置文件
if [ ! -f ".env.test" ]; then
    echo "❌ 测试配置文件 .env.test 不存在"
    echo "请创建 .env.test 文件，参考 .env.test.example"
    exit 1
fi

echo "✅ 测试配置文件存在"

# 检查数据库连接
echo "📊 检查数据库连接..."
if ! go run cmd/dbtest/main.go > /dev/null 2>&1; then
    echo "❌ 数据库连接失败"
    echo "请确保数据库服务正在运行，并且配置正确"
    exit 1
fi

echo "✅ 数据库连接正常"

# 清理测试数据
echo "🧹 清理测试数据..."
go run cmd/clean/main.go

# 运行数据库迁移
echo "📦 运行数据库迁移..."
go run cmd/migrate/main.go

# 运行单元测试
echo ""
echo "🔧 运行单元测试..."
echo "==================="

echo "📝 工具函数测试..."
go test ./pkg/utils/... -v

echo ""
echo "🔒 中间件测试..."
go test ./internal/middleware/... -v

# 运行API集成测试
echo ""
echo "🌐 运行API集成测试..."
echo "===================="

echo "📡 全覆盖API测试..."
go test ./tests/... -v -timeout=30s

# 生成测试覆盖率报告
echo ""
echo "📊 生成测试覆盖率报告..."
echo "========================"

go test ./... -coverprofile=coverage.out
go tool cover -html=coverage.out -o coverage.html

echo ""
echo "✅ 测试完成！"
echo "============="
echo "📄 覆盖率报告已生成: coverage.html"
echo "🔍 在浏览器中打开 coverage.html 查看详细覆盖率"

# 显示测试总结
echo ""
echo "📋 测试总结:"
echo "============"
echo "✅ 健康检查测试"
echo "✅ 用户注册测试"
echo "✅ 用户登录测试"
echo "✅ 获取用户信息测试"
echo "✅ 刷新令牌测试"
echo "✅ 创建情侣关系测试"
echo "✅ 加入情侣关系测试"
echo "✅ 获取情侣信息测试"
echo "✅ 创建心情记录测试"
echo "✅ 更新心情记录测试"
echo "✅ 获取情侣心情记录测试"
echo "✅ 删除心情记录测试"
echo "✅ 获取最后同步时间测试"
echo "✅ 获取同步变更测试"
echo "✅ 上传同步变更测试"
echo "✅ 离开情侣关系测试"
echo "✅ 用户登出测试"
echo "✅ 错误情况测试"
echo "✅ 参数验证测试"

echo ""
echo "🎉 所有测试通过！API文档与实际实现完全一致！"
