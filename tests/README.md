# MoodTracker API 全覆盖测试

## 概述

本测试套件根据 `docs/API.md` 文档编写，对所有API端点进行全覆盖测试，验证请求方法、参数、响应格式是否与API文档完全一致。

## 测试覆盖范围

### 🔍 API端点覆盖（27个端点）

#### 认证相关 API (4个)
- ✅ `POST /api/v1/auth/register` - 用户注册
- ✅ `POST /api/v1/auth/login` - 用户登录  
- ✅ `POST /api/v1/auth/refresh` - 刷新令牌
- ✅ `POST /api/v1/auth/logout` - 用户登出

#### 用户相关 API (1个)
- ✅ `GET /api/v1/user/me` - 获取当前用户信息

#### 情侣关系 API (4个)
- ✅ `POST /api/v1/couples/create` - 创建情侣关系
- ✅ `POST /api/v1/couples/join` - 加入情侣关系
- ✅ `GET /api/v1/couples/info` - 获取情侣信息
- ✅ `DELETE /api/v1/couples/leave` - 离开情侣关系

#### 心情记录 API (4个)
- ✅ `POST /api/v1/moods` - 创建心情记录
- ✅ `PUT /api/v1/moods/{entryId}` - 更新心情记录
- ✅ `DELETE /api/v1/moods/{entryId}` - 删除心情记录
- ✅ `GET /api/v1/moods/couple` - 获取情侣心情记录

#### 数据同步 API (3个)
- ✅ `GET /api/v1/sync/changes` - 获取增量变更
- ✅ `POST /api/v1/sync/upload` - 批量上传变更
- ✅ `GET /api/v1/sync/last-sync-time` - 获取最后同步时间

#### 其他 API (1个)
- ✅ `GET /health` - 健康检查

### 📋 验证内容

#### 1. HTTP方法验证
- 验证每个端点使用正确的HTTP方法（GET、POST、PUT、DELETE）

#### 2. 请求格式验证
- 验证请求头（Content-Type、Authorization）
- 验证请求体结构和字段类型
- 验证路径参数和查询参数

#### 3. 响应格式验证
- 验证HTTP状态码
- 验证通用响应格式（success、message、data、error、timestamp）
- 验证响应数据结构和字段类型
- 验证字段值的正确性

#### 4. 业务逻辑验证
- 验证认证流程（注册→登录→获取用户信息→刷新令牌→登出）
- 验证情侣关系流程（创建→加入→获取信息→离开）
- 验证心情记录流程（创建→更新→获取→删除）
- 验证数据同步流程（获取变更→上传变更→获取同步时间）

#### 5. 错误处理验证
- 验证无效认证令牌的处理
- 验证参数验证错误的处理
- 验证业务逻辑错误的处理
- 验证错误响应格式

## 测试文件结构

```
tests/
├── README.md                    # 本文档
├── api_comprehensive_test.go    # 全覆盖API测试套件
├── run_tests.sh                # 测试运行脚本
└── .env.test.example           # 测试配置文件示例
```

## 运行测试

### 1. 环境准备

```bash
# 复制测试配置文件
cp .env.test.example .env.test

# 根据实际环境修改配置
vim .env.test
```

### 2. 运行完整测试

```bash
# 使用测试脚本（推荐）
./tests/run_tests.sh

# 或者手动运行
go test ./tests/... -v
```

### 3. 运行特定测试

```bash
# 只运行API测试
go test ./tests/api_comprehensive_test.go -v

# 运行特定测试方法
go test ./tests/... -v -run TestHealthCheck
```

## 测试数据管理

### 自动化数据管理
- 测试开始前自动清理数据库
- 测试过程中自动创建测试数据
- 测试结束后自动清理测试数据

### 测试数据隔离
- 使用独立的测试数据库
- 每个测试用例使用唯一的测试数据
- 避免测试用例之间的数据污染

## 测试结果验证

### 成功标准
- 所有HTTP状态码正确
- 所有响应格式符合API文档
- 所有字段类型和值正确
- 所有业务逻辑按预期工作

### 失败处理
- 详细的错误信息输出
- 失败测试的具体位置
- 期望值与实际值的对比

## 持续集成

### GitHub Actions 配置示例

```yaml
name: API Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: moodtracker_test
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-go@v2
        with:
          go-version: 1.21
      - name: Run tests
        run: ./tests/run_tests.sh
```

## 测试覆盖率

目标覆盖率：
- API端点覆盖率：100%
- 响应格式覆盖率：100%
- 错误情况覆盖率：90%+
- 业务逻辑覆盖率：95%+

## 维护说明

### 更新测试
当API文档更新时，需要相应更新测试用例：
1. 检查新增的API端点
2. 检查修改的请求/响应格式
3. 检查新增的错误情况
4. 更新测试用例和验证逻辑

### 测试最佳实践
1. 保持测试用例与API文档同步
2. 使用描述性的测试方法名
3. 添加详细的测试注释
4. 验证所有重要的响应字段
5. 测试正常情况和异常情况

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否运行
   - 检查 .env.test 配置是否正确

2. **测试超时**
   - 检查数据库性能
   - 增加测试超时时间

3. **认证失败**
   - 检查JWT配置
   - 检查令牌生成逻辑

4. **数据不一致**
   - 运行数据库清理脚本
   - 检查测试数据隔离

## 贡献指南

1. 新增API端点时，必须添加对应的测试用例
2. 修改API响应格式时，必须更新相关测试
3. 所有测试必须通过才能合并代码
4. 保持测试覆盖率在95%以上
