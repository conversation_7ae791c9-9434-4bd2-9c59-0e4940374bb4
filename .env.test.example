# MoodTracker API 测试环境配置文件示例
# 复制此文件为 .env.test 并根据实际环境修改配置

# 服务器配置
SERVER_PORT=8080
SERVER_GIN_MODE=test

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=woxiang1
DB_NAME=moodtracker_test

# JWT配置
JWT_SECRET=test-secret-key-for-comprehensive-testing
JWT_EXPIRES_IN=3600

# 日志配置
LOG_LEVEL=error

# 测试说明
# 1. 请确保数据库服务正在运行
# 2. 建议使用独立的测试数据库（如 moodtracker_test）
# 3. 测试会自动清理和重建数据
# 4. JWT_SECRET 在测试环境中可以使用固定值
# 5. LOG_LEVEL 设置为 error 以减少测试输出噪音
