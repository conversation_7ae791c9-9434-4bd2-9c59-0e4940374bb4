#!/bin/bash

# MoodTracker 数据库管理主脚本
# 提供数据库的创建、验证、清理等完整管理功能

set -e

echo "🗄️  MoodTracker 数据库管理器"
echo "============================"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示菜单
show_menu() {
    echo -e "${CYAN}请选择操作:${NC}"
    echo ""
    echo "1. 🏗️  创建数据库表"
    echo "2. 🔍 验证数据库架构"
    echo "3. 🧹 清理数据库数据"
    echo "4. 📊 查看数据库状态"
    echo "5. 🔄 运行数据库迁移"
    echo "6. 📋 导出数据库结构"
    echo "7. 📥 导入测试数据"
    echo "8. 🔧 数据库连接测试"
    echo "9. 📖 查看帮助信息"
    echo "0. 🚪 退出"
    echo ""
}

# 获取数据库连接信息
get_db_config() {
    if [ -z "$DB_HOST" ]; then
        echo -e "${BLUE}请输入数据库连接信息:${NC}"
        echo ""
        
        read -p "数据库主机 (默认: localhost): " DB_HOST
        DB_HOST=${DB_HOST:-localhost}
        
        read -p "数据库端口 (默认: 3306): " DB_PORT
        DB_PORT=${DB_PORT:-3306}
        
        read -p "数据库用户名 (默认: root): " DB_USERNAME
        DB_USERNAME=${DB_USERNAME:-root}
        
        read -s -p "数据库密码: " DB_PASSWORD
        echo ""
        
        read -p "数据库名称 (默认: moodtracker): " DB_NAME
        DB_NAME=${DB_NAME:-moodtracker}
        
        # 构建MySQL连接命令和配置文件
        MYSQL_CONFIG_FILE=$(mktemp)
        cat > "$MYSQL_CONFIG_FILE" << EOF
[client]
host=$DB_HOST
port=$DB_PORT
user=$DB_USERNAME
password=$DB_PASSWORD
EOF

        # 设置配置文件权限
        chmod 600 "$MYSQL_CONFIG_FILE"

        MYSQL_CMD="mysql --defaults-file=$MYSQL_CONFIG_FILE"

        # 清理函数
        cleanup() {
            if [ -f "$MYSQL_CONFIG_FILE" ]; then
                rm -f "$MYSQL_CONFIG_FILE"
            fi
        }

        # 设置退出时清理
        trap cleanup EXIT
        
        echo ""
        echo -e "${GREEN}✅ 数据库配置已设置${NC}"
        echo ""
    fi
}

# 测试数据库连接
test_connection() {
    echo "🔍 测试数据库连接..."
    
    if echo "SELECT 1;" | $MYSQL_CMD $DB_NAME >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 数据库连接成功${NC}"
        return 0
    else
        echo -e "${RED}❌ 数据库连接失败${NC}"
        return 1
    fi
}

# 创建数据库表
create_tables() {
    echo -e "${BLUE}🏗️  创建数据库表...${NC}"
    echo ""
    
    if [ -f "scripts/create_database_tables.sh" ]; then
        # 传递数据库配置给子脚本
        export DB_HOST DB_PORT DB_USERNAME DB_PASSWORD DB_NAME
        echo "y" | ./scripts/create_database_tables.sh
    else
        echo -e "${RED}❌ 创建表脚本不存在: scripts/create_database_tables.sh${NC}"
        return 1
    fi
}

# 验证数据库架构
validate_schema() {
    echo -e "${BLUE}🔍 验证数据库架构...${NC}"
    echo ""
    
    if [ -f "scripts/validate_database_schema.sh" ]; then
        # 传递数据库配置给子脚本
        export DB_HOST DB_PORT DB_USERNAME DB_PASSWORD DB_NAME
        ./scripts/validate_database_schema.sh
    else
        echo -e "${RED}❌ 验证脚本不存在: scripts/validate_database_schema.sh${NC}"
        return 1
    fi
}

# 清理数据库数据
clean_database() {
    echo -e "${YELLOW}🧹 清理数据库数据...${NC}"
    echo ""
    
    echo -e "${RED}⚠️  警告: 此操作将删除所有数据！${NC}"
    read -p "确认清理数据库? (y/N): " CONFIRM
    
    if [[ $CONFIRM =~ ^[Yy]$ ]]; then
        if [ -f "cmd/clean/main.go" ]; then
            echo "运行清理程序..."
            go run cmd/clean/main.go
        else
            echo "使用SQL清理..."
            $MYSQL_CMD $DB_NAME << 'EOF'
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE refresh_tokens;
TRUNCATE TABLE moods;
TRUNCATE TABLE couples;
TRUNCATE TABLE users;
TRUNCATE TABLE migrations;
SET FOREIGN_KEY_CHECKS = 1;
EOF
            echo -e "${GREEN}✅ 数据库清理完成${NC}"
        fi
    else
        echo "操作已取消"
    fi
}

# 查看数据库状态
show_database_status() {
    echo -e "${BLUE}📊 数据库状态信息${NC}"
    echo "=================="
    echo ""
    
    # 显示表和记录数
    echo "📋 表和记录统计:"
    TABLES=("users" "couples" "moods" "refresh_tokens" "migrations")
    
    for table in "${TABLES[@]}"; do
        if $MYSQL_CMD $DB_NAME -e "SHOW TABLES LIKE '$table';" | grep -q "$table"; then
            COUNT=$($MYSQL_CMD $DB_NAME -se "SELECT COUNT(*) FROM $table;" 2>/dev/null || echo "0")
            echo "  $table: $COUNT 条记录"
        else
            echo "  $table: 表不存在"
        fi
    done
    
    echo ""
    echo "💾 数据库信息:"
    DB_SIZE=$($MYSQL_CMD $DB_NAME -se "
        SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'DB Size in MB'
        FROM information_schema.tables
        WHERE table_schema='$DB_NAME';
    " 2>/dev/null || echo "未知")
    echo "  数据库大小: ${DB_SIZE} MB"
    
    TABLE_COUNT=$($MYSQL_CMD $DB_NAME -se "
        SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$DB_NAME';
    " 2>/dev/null || echo "0")
    echo "  表数量: $TABLE_COUNT"
}

# 运行数据库迁移
run_migrations() {
    echo -e "${BLUE}🔄 运行数据库迁移...${NC}"
    echo ""
    
    if [ -f "cmd/migrate/main.go" ]; then
        go run cmd/migrate/main.go
    else
        echo -e "${YELLOW}⚠️  迁移程序不存在，跳过迁移${NC}"
    fi
}

# 导出数据库结构
export_schema() {
    echo -e "${BLUE}📋 导出数据库结构...${NC}"
    echo ""
    
    OUTPUT_FILE="database_schema_$(date +%Y%m%d_%H%M%S).sql"
    
    mysqldump --defaults-file="$MYSQL_CONFIG_FILE" \
        --no-data --routines --triggers $DB_NAME > $OUTPUT_FILE
    
    echo -e "${GREEN}✅ 数据库结构已导出到: $OUTPUT_FILE${NC}"
}

# 导入测试数据
import_test_data() {
    echo -e "${BLUE}📥 导入测试数据...${NC}"
    echo ""
    
    # 创建示例测试数据
    $MYSQL_CMD $DB_NAME << 'EOF'
-- 插入测试用户
INSERT IGNORE INTO users (user_id, username, password_hash, nickname, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'testuser1', '$2a$10$example1', '测试用户1', NOW()),
('550e8400-e29b-41d4-a716-446655440002', 'testuser2', '$2a$10$example2', '测试用户2', NOW());

-- 插入测试情侣关系
INSERT IGNORE INTO couples (couple_id, user1_id, user2_id, relationship_name, status, created_at, matched_at) VALUES
('660e8400-e29b-41d4-a716-446655440001', 
 '550e8400-e29b-41d4-a716-446655440001', 
 '550e8400-e29b-41d4-a716-446655440002', 
 '测试情侣关系', 'matched', NOW(), NOW());

-- 插入测试心情记录
INSERT IGNORE INTO moods (entry_id, user_id, couple_id, date, mood, note, created_at, updated_at) VALUES
('770e8400-e29b-41d4-a716-446655440001', 
 '550e8400-e29b-41d4-a716-446655440001', 
 '660e8400-e29b-41d4-a716-446655440001', 
 CURDATE(), 8, '今天心情不错', NOW(), NOW()),
('770e8400-e29b-41d4-a716-446655440002', 
 '550e8400-e29b-41d4-a716-446655440002', 
 '660e8400-e29b-41d4-a716-446655440001', 
 CURDATE(), 9, '心情很好', NOW(), NOW());
EOF
    
    echo -e "${GREEN}✅ 测试数据导入完成${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${CYAN}📖 MoodTracker 数据库管理器帮助${NC}"
    echo "================================="
    echo ""
    echo "此工具提供 MoodTracker API 数据库的完整管理功能："
    echo ""
    echo "🏗️  创建数据库表:"
    echo "   根据 docs/DATABASE.md 文档创建所有必需的表"
    echo "   包括用户、情侣关系、心情记录、刷新令牌等表"
    echo ""
    echo "🔍 验证数据库架构:"
    echo "   检查实际数据库结构与文档的一致性"
    echo "   验证表结构、字段类型、索引、外键等"
    echo ""
    echo "🧹 清理数据库数据:"
    echo "   安全地清理所有表中的数据，保留表结构"
    echo ""
    echo "📊 查看数据库状态:"
    echo "   显示表统计信息、数据库大小等状态"
    echo ""
    echo "🔄 运行数据库迁移:"
    echo "   执行数据库版本迁移脚本"
    echo ""
    echo "📋 导出数据库结构:"
    echo "   导出当前数据库的表结构到SQL文件"
    echo ""
    echo "📥 导入测试数据:"
    echo "   导入预定义的测试数据用于开发和测试"
    echo ""
    echo "🔧 数据库连接测试:"
    echo "   测试数据库连接是否正常"
    echo ""
    echo -e "${YELLOW}注意事项:${NC}"
    echo "- 请确保数据库服务正在运行"
    echo "- 建议在测试环境中使用"
    echo "- 清理操作不可逆，请谨慎使用"
    echo "- 相关文档: docs/DATABASE.md"
}

# 主循环
main() {
    while true; do
        show_menu
        read -p "请选择操作 (0-9): " choice
        echo ""
        
        case $choice in
            1)
                get_db_config
                if test_connection; then
                    create_tables
                fi
                ;;
            2)
                get_db_config
                if test_connection; then
                    validate_schema
                fi
                ;;
            3)
                get_db_config
                if test_connection; then
                    clean_database
                fi
                ;;
            4)
                get_db_config
                if test_connection; then
                    show_database_status
                fi
                ;;
            5)
                get_db_config
                if test_connection; then
                    run_migrations
                fi
                ;;
            6)
                get_db_config
                if test_connection; then
                    export_schema
                fi
                ;;
            7)
                get_db_config
                if test_connection; then
                    import_test_data
                fi
                ;;
            8)
                get_db_config
                test_connection
                ;;
            9)
                show_help
                ;;
            0)
                echo -e "${GREEN}👋 再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请输入 0-9${NC}"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..."
        echo ""
    done
}

# 运行主程序
main
