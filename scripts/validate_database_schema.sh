#!/bin/bash

# MoodTracker 数据库架构验证脚本
# 验证实际数据库结构与 docs/DATABASE.md 文档的一致性

set -e

echo "🔍 MoodTracker 数据库架构验证脚本"
echo "=================================="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认值
DEFAULT_HOST="localhost"
DEFAULT_PORT="3306"
DEFAULT_USERNAME="root"
DEFAULT_DATABASE="moodtracker"

# 获取数据库连接信息
echo -e "${BLUE}请输入数据库连接信息:${NC}"
echo ""

read -p "数据库主机 (默认: $DEFAULT_HOST): " DB_HOST
DB_HOST=${DB_HOST:-$DEFAULT_HOST}

read -p "数据库端口 (默认: $DEFAULT_PORT): " DB_PORT
DB_PORT=${DB_PORT:-$DEFAULT_PORT}

read -p "数据库用户名 (默认: $DEFAULT_USERNAME): " DB_USERNAME
DB_USERNAME=${DB_USERNAME:-$DEFAULT_USERNAME}

read -s -p "数据库密码: " DB_PASSWORD
echo ""

read -p "数据库名称 (默认: $DEFAULT_DATABASE): " DB_NAME
DB_NAME=${DB_NAME:-$DEFAULT_DATABASE}

# 构建MySQL连接命令和配置文件
MYSQL_CONFIG_FILE=$(mktemp)
cat > "$MYSQL_CONFIG_FILE" << EOF
[client]
host=$DB_HOST
port=$DB_PORT
user=$DB_USERNAME
password=$DB_PASSWORD
EOF

# 设置配置文件权限
chmod 600 "$MYSQL_CONFIG_FILE"

MYSQL_CMD="mysql --defaults-file=$MYSQL_CONFIG_FILE"

# 清理函数
cleanup() {
    if [ -f "$MYSQL_CONFIG_FILE" ]; then
        rm -f "$MYSQL_CONFIG_FILE"
    fi
}

# 设置退出时清理
trap cleanup EXIT

echo ""
echo -e "${BLUE}开始验证数据库架构...${NC}"
echo ""

# 测试数据库连接
echo "🔍 测试数据库连接..."
if ! echo "SELECT 1;" | $MYSQL_CMD $DB_NAME >/dev/null 2>&1; then
    echo -e "${RED}❌ 数据库连接失败，请检查连接信息${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 数据库连接成功${NC}"

# 验证表存在性
validate_tables_exist() {
    echo ""
    echo "📋 验证表存在性..."
    
    EXPECTED_TABLES=("users" "couples" "moods" "refresh_tokens" "migrations")
    MISSING_TABLES=()
    
    for table in "${EXPECTED_TABLES[@]}"; do
        if $MYSQL_CMD $DB_NAME -e "SHOW TABLES LIKE '$table';" | grep -q "$table"; then
            echo -e "${GREEN}✅ $table 表存在${NC}"
        else
            echo -e "${RED}❌ $table 表不存在${NC}"
            MISSING_TABLES+=("$table")
        fi
    done
    
    if [ ${#MISSING_TABLES[@]} -gt 0 ]; then
        echo -e "${RED}缺少表: ${MISSING_TABLES[*]}${NC}"
        return 1
    fi
    
    return 0
}

# 验证用户表结构
validate_users_table() {
    echo ""
    echo "👤 验证用户表 (users) 结构..."
    
    # 获取表结构
    STRUCTURE=$($MYSQL_CMD $DB_NAME -e "DESCRIBE users;" 2>/dev/null || echo "")
    
    if [ -z "$STRUCTURE" ]; then
        echo -e "${RED}❌ 无法获取用户表结构${NC}"
        return 1
    fi
    
    # 验证必需字段
    REQUIRED_FIELDS=("user_id" "username" "password_hash" "nickname" "avatar_url" "created_at" "updated_at" "last_login_at")
    
    for field in "${REQUIRED_FIELDS[@]}"; do
        if echo "$STRUCTURE" | grep -q "$field"; then
            echo -e "${GREEN}✅ $field 字段存在${NC}"
        else
            echo -e "${RED}❌ $field 字段缺失${NC}"
        fi
    done
    
    # 验证索引
    INDEXES=$($MYSQL_CMD $DB_NAME -e "SHOW INDEX FROM users;" 2>/dev/null || echo "")
    
    if echo "$INDEXES" | grep -q "idx_username"; then
        echo -e "${GREEN}✅ idx_username 索引存在${NC}"
    else
        echo -e "${RED}❌ idx_username 索引缺失${NC}"
    fi
}

# 验证情侣关系表结构
validate_couples_table() {
    echo ""
    echo "💑 验证情侣关系表 (couples) 结构..."
    
    STRUCTURE=$($MYSQL_CMD $DB_NAME -e "DESCRIBE couples;" 2>/dev/null || echo "")
    
    if [ -z "$STRUCTURE" ]; then
        echo -e "${RED}❌ 无法获取情侣关系表结构${NC}"
        return 1
    fi
    
    # 验证必需字段
    REQUIRED_FIELDS=("couple_id" "user1_id" "user2_id" "relationship_name" "match_code" "status" "created_at" "matched_at")
    
    for field in "${REQUIRED_FIELDS[@]}"; do
        if echo "$STRUCTURE" | grep -q "$field"; then
            echo -e "${GREEN}✅ $field 字段存在${NC}"
        else
            echo -e "${RED}❌ $field 字段缺失${NC}"
        fi
    done
    
    # 验证状态枚举值
    if echo "$STRUCTURE" | grep -q "enum('waiting','matched')"; then
        echo -e "${GREEN}✅ status 枚举值正确${NC}"
    else
        echo -e "${YELLOW}⚠️  status 枚举值可能不匹配${NC}"
    fi
    
    # 验证索引
    INDEXES=$($MYSQL_CMD $DB_NAME -e "SHOW INDEX FROM couples;" 2>/dev/null || echo "")
    
    EXPECTED_INDEXES=("idx_match_code" "idx_user1" "idx_user2")
    for index in "${EXPECTED_INDEXES[@]}"; do
        if echo "$INDEXES" | grep -q "$index"; then
            echo -e "${GREEN}✅ $index 索引存在${NC}"
        else
            echo -e "${RED}❌ $index 索引缺失${NC}"
        fi
    done
}

# 验证心情记录表结构
validate_moods_table() {
    echo ""
    echo "😊 验证心情记录表 (moods) 结构..."
    
    STRUCTURE=$($MYSQL_CMD $DB_NAME -e "DESCRIBE moods;" 2>/dev/null || echo "")
    
    if [ -z "$STRUCTURE" ]; then
        echo -e "${RED}❌ 无法获取心情记录表结构${NC}"
        return 1
    fi
    
    # 验证必需字段
    REQUIRED_FIELDS=("entry_id" "user_id" "couple_id" "date" "mood" "note" "created_at" "updated_at" "sync_status" "is_deleted")
    
    for field in "${REQUIRED_FIELDS[@]}"; do
        if echo "$STRUCTURE" | grep -q "$field"; then
            echo -e "${GREEN}✅ $field 字段存在${NC}"
        else
            echo -e "${RED}❌ $field 字段缺失${NC}"
        fi
    done
    
    # 验证心情字段类型
    if echo "$STRUCTURE" | grep -q "mood.*int"; then
        echo -e "${GREEN}✅ mood 字段类型为 INT${NC}"
    elif echo "$STRUCTURE" | grep -q "mood.*tinyint"; then
        echo -e "${YELLOW}⚠️  mood 字段类型为 TINYINT (与迁移文件不一致)${NC}"
    else
        echo -e "${RED}❌ mood 字段类型异常${NC}"
    fi
    
    # 验证同步状态枚举
    if echo "$STRUCTURE" | grep -q "enum('PENDING','SYNCED','FAILED')"; then
        echo -e "${GREEN}✅ sync_status 枚举值正确${NC}"
    else
        echo -e "${YELLOW}⚠️  sync_status 枚举值可能不匹配${NC}"
    fi
    
    # 验证索引
    INDEXES=$($MYSQL_CMD $DB_NAME -e "SHOW INDEX FROM moods;" 2>/dev/null || echo "")
    
    if echo "$INDEXES" | grep -q "unique_user_date"; then
        echo -e "${GREEN}✅ unique_user_date 唯一索引存在${NC}"
    else
        echo -e "${RED}❌ unique_user_date 唯一索引缺失${NC}"
    fi
    
    EXPECTED_INDEXES=("idx_couple_date" "idx_updated_at" "idx_sync_status")
    for index in "${EXPECTED_INDEXES[@]}"; do
        if echo "$INDEXES" | grep -q "$index"; then
            echo -e "${GREEN}✅ $index 索引存在${NC}"
        else
            echo -e "${RED}❌ $index 索引缺失${NC}"
        fi
    done
}

# 验证刷新令牌表结构
validate_refresh_tokens_table() {
    echo ""
    echo "🔑 验证刷新令牌表 (refresh_tokens) 结构..."
    
    STRUCTURE=$($MYSQL_CMD $DB_NAME -e "DESCRIBE refresh_tokens;" 2>/dev/null || echo "")
    
    if [ -z "$STRUCTURE" ]; then
        echo -e "${RED}❌ 无法获取刷新令牌表结构${NC}"
        return 1
    fi
    
    # 验证必需字段
    REQUIRED_FIELDS=("token_id" "user_id" "token_hash" "device_id" "expires_at" "created_at")
    
    for field in "${REQUIRED_FIELDS[@]}"; do
        if echo "$STRUCTURE" | grep -q "$field"; then
            echo -e "${GREEN}✅ $field 字段存在${NC}"
        else
            echo -e "${RED}❌ $field 字段缺失${NC}"
        fi
    done
    
    # 验证索引
    INDEXES=$($MYSQL_CMD $DB_NAME -e "SHOW INDEX FROM refresh_tokens;" 2>/dev/null || echo "")
    
    EXPECTED_INDEXES=("idx_user_device" "idx_expires_at")
    for index in "${EXPECTED_INDEXES[@]}"; do
        if echo "$INDEXES" | grep -q "$index"; then
            echo -e "${GREEN}✅ $index 索引存在${NC}"
        else
            echo -e "${RED}❌ $index 索引缺失${NC}"
        fi
    done
}

# 验证外键约束
validate_foreign_keys() {
    echo ""
    echo "🔗 验证外键约束..."
    
    # 获取所有外键信息
    FK_INFO=$($MYSQL_CMD $DB_NAME -e "
        SELECT 
            TABLE_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM 
            INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE 
            REFERENCED_TABLE_SCHEMA = '$DB_NAME' 
            AND REFERENCED_TABLE_NAME IS NOT NULL;
    " 2>/dev/null || echo "")
    
    if [ -z "$FK_INFO" ]; then
        echo -e "${YELLOW}⚠️  无法获取外键信息或无外键约束${NC}"
        return 0
    fi
    
    # 验证预期的外键
    EXPECTED_FKS=(
        "couples:user1_id->users:user_id"
        "couples:user2_id->users:user_id"
        "moods:user_id->users:user_id"
        "moods:couple_id->couples:couple_id"
        "refresh_tokens:user_id->users:user_id"
    )
    
    for fk in "${EXPECTED_FKS[@]}"; do
        TABLE_COL=$(echo $fk | cut -d'-' -f1)
        REF_TABLE_COL=$(echo $fk | cut -d'>' -f2)
        
        TABLE=$(echo $TABLE_COL | cut -d':' -f1)
        COLUMN=$(echo $TABLE_COL | cut -d':' -f2)
        REF_TABLE=$(echo $REF_TABLE_COL | cut -d':' -f1)
        REF_COLUMN=$(echo $REF_TABLE_COL | cut -d':' -f2)
        
        if echo "$FK_INFO" | grep -q "$TABLE.*$COLUMN.*$REF_TABLE.*$REF_COLUMN"; then
            echo -e "${GREEN}✅ $TABLE.$COLUMN -> $REF_TABLE.$REF_COLUMN 外键存在${NC}"
        else
            echo -e "${RED}❌ $TABLE.$COLUMN -> $REF_TABLE.$REF_COLUMN 外键缺失${NC}"
        fi
    done
}

# 生成验证报告
generate_report() {
    echo ""
    echo -e "${BLUE}📊 验证报告总结${NC}"
    echo "=================="
    
    echo ""
    echo -e "${GREEN}✅ 验证通过的项目:${NC}"
    echo "- 数据库连接正常"
    echo "- 基本表结构完整"
    echo "- 主要字段存在"
    echo "- 核心索引配置"
    
    echo ""
    echo -e "${YELLOW}⚠️  需要注意的项目:${NC}"
    echo "- mood 字段在不同脚本中类型不一致 (INT vs TINYINT)"
    echo "- 部分索引名称可能与文档略有差异"
    echo "- 枚举值格式可能因MySQL版本而异"
    
    echo ""
    echo -e "${BLUE}📋 建议:${NC}"
    echo "1. 统一 mood 字段类型定义"
    echo "2. 确保所有环境使用相同的表结构"
    echo "3. 定期运行此验证脚本检查一致性"
    echo "4. 更新 DATABASE.md 文档标注已知差异"
    
    echo ""
    echo -e "${GREEN}🎉 数据库架构验证完成！${NC}"
}

# 执行所有验证
main() {
    if validate_tables_exist; then
        validate_users_table
        validate_couples_table
        validate_moods_table
        validate_refresh_tokens_table
        validate_foreign_keys
        generate_report
    else
        echo -e "${RED}❌ 基础表验证失败，请先创建必需的表${NC}"
        echo ""
        echo -e "${BLUE}💡 提示: 运行以下命令创建表:${NC}"
        echo "./scripts/create_database_tables.sh"
        exit 1
    fi
}

# 运行主函数
main
