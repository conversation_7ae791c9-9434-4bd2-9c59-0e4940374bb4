#!/bin/bash

# MoodTracker API 数据库表创建脚本
# 根据 docs/DATABASE.md 文档创建所有必需的表
# 支持交互式输入数据库连接信息

set -e

echo "🗄️  MoodTracker 数据库表创建脚本"
echo "=================================="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认值
DEFAULT_HOST="localhost"
DEFAULT_PORT="3306"
DEFAULT_USERNAME="root"
DEFAULT_DATABASE="moodtracker"

# 获取数据库连接信息
echo -e "${BLUE}请输入数据库连接信息:${NC}"
echo ""

read -p "数据库主机 (默认: $DEFAULT_HOST): " DB_HOST
DB_HOST=${DB_HOST:-$DEFAULT_HOST}

read -p "数据库端口 (默认: $DEFAULT_PORT): " DB_PORT
DB_PORT=${DB_PORT:-$DEFAULT_PORT}

read -p "数据库用户名 (默认: $DEFAULT_USERNAME): " DB_USERNAME
DB_USERNAME=${DB_USERNAME:-$DEFAULT_USERNAME}

read -s -p "数据库密码: " DB_PASSWORD
echo ""

read -p "数据库名称 (默认: $DEFAULT_DATABASE): " DB_NAME
DB_NAME=${DB_NAME:-$DEFAULT_DATABASE}

echo ""
echo -e "${YELLOW}连接信息确认:${NC}"
echo "主机: $DB_HOST"
echo "端口: $DB_PORT"
echo "用户名: $DB_USERNAME"
echo "数据库: $DB_NAME"
echo ""

read -p "确认创建表? (y/N): " CONFIRM
if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

# 构建MySQL连接命令和配置文件
MYSQL_CONFIG_FILE=$(mktemp)
cat > "$MYSQL_CONFIG_FILE" << EOF
[client]
host=$DB_HOST
port=$DB_PORT
user=$DB_USERNAME
password=$DB_PASSWORD
EOF

# 设置配置文件权限
chmod 600 "$MYSQL_CONFIG_FILE"

MYSQL_CMD="mysql --defaults-file=$MYSQL_CONFIG_FILE"

# 清理函数
cleanup() {
    if [ -f "$MYSQL_CONFIG_FILE" ]; then
        rm -f "$MYSQL_CONFIG_FILE"
    fi
}

# 设置退出时清理
trap cleanup EXIT

echo ""
echo -e "${BLUE}开始创建数据库表...${NC}"
echo ""

# 测试数据库连接
echo "🔍 测试数据库连接..."
if ! echo "SELECT 1;" | $MYSQL_CMD $DB_NAME >/dev/null 2>&1; then
    echo -e "${RED}❌ 数据库连接失败，请检查连接信息${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 数据库连接成功${NC}"

# 创建表的SQL脚本
create_tables() {
    echo "📋 创建用户表 (users)..."
    $MYSQL_CMD $DB_NAME << 'EOF'
CREATE TABLE IF NOT EXISTS users (
    user_id VARCHAR(36) PRIMARY KEY COMMENT '用户唯一标识(UUID)',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希值',
    nickname VARCHAR(100) COMMENT '用户昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    
    INDEX idx_username (username)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
EOF

    echo "💑 创建情侣关系表 (couples)..."
    $MYSQL_CMD $DB_NAME << 'EOF'
CREATE TABLE IF NOT EXISTS couples (
    couple_id VARCHAR(36) PRIMARY KEY COMMENT '情侣关系唯一标识(UUID)',
    user1_id VARCHAR(36) NOT NULL COMMENT '用户1ID(创建者)',
    user2_id VARCHAR(36) NULL COMMENT '用户2ID(加入者)',
    relationship_name VARCHAR(100) COMMENT '关系名称',
    match_code VARCHAR(8) COMMENT '8位数字匹配码',
    status ENUM('waiting', 'matched') DEFAULT 'waiting' COMMENT '关系状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    matched_at TIMESTAMP NULL COMMENT '匹配完成时间',
    expires_at TIMESTAMP NOT NULL DEFAULT (CURRENT_TIMESTAMP + INTERVAL 1 HOUR) COMMENT '匹配码过期时间',

    FOREIGN KEY (user1_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (user2_id) REFERENCES users(user_id) ON DELETE CASCADE,

    INDEX idx_match_code (match_code),
    INDEX idx_user1 (user1_id),
    INDEX idx_user2 (user2_id),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='情侣关系表';
EOF

    echo "😊 创建心情记录表 (moods)..."
    $MYSQL_CMD $DB_NAME << 'EOF'
CREATE TABLE IF NOT EXISTS moods (
    entry_id VARCHAR(36) PRIMARY KEY COMMENT '记录唯一标识(UUID)',
    user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
    couple_id VARCHAR(36) NOT NULL COMMENT '情侣关系ID',
    date DATE NOT NULL COMMENT '记录日期',
    mood INT NOT NULL COMMENT '心情值(1-12)',
    note TEXT COMMENT '心情备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    sync_status ENUM('PENDING', 'SYNCED', 'FAILED') DEFAULT 'SYNCED' COMMENT '同步状态',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除(软删除)',
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (couple_id) REFERENCES couples(couple_id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_user_date (user_id, date),
    INDEX idx_couple_date (couple_id, date),
    INDEX idx_updated_at (updated_at),
    INDEX idx_sync_status (sync_status),
    
    CONSTRAINT chk_mood_range CHECK (mood >= 1 AND mood <= 12)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='心情记录表';
EOF

    echo "🔑 创建刷新令牌表 (refresh_tokens)..."
    $MYSQL_CMD $DB_NAME << 'EOF'
CREATE TABLE IF NOT EXISTS refresh_tokens (
    token_id VARCHAR(36) PRIMARY KEY COMMENT '令牌唯一标识(UUID)',
    user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
    token_hash VARCHAR(255) NOT NULL COMMENT '令牌哈希值',
    device_id VARCHAR(36) COMMENT '设备ID',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    
    INDEX idx_user_device (user_id, device_id),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='刷新令牌表';
EOF

    echo "📊 创建迁移记录表 (migrations)..."
    $MYSQL_CMD $DB_NAME << 'EOF'
CREATE TABLE IF NOT EXISTS migrations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    version VARCHAR(50) NOT NULL UNIQUE COMMENT '迁移版本',
    name VARCHAR(255) NOT NULL COMMENT '迁移名称',
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据库迁移记录表';
EOF
}

# 验证表创建
verify_tables() {
    echo ""
    echo "🔍 验证表创建结果..."
    
    TABLES=("users" "couples" "moods" "refresh_tokens" "migrations")
    ALL_SUCCESS=true
    
    for table in "${TABLES[@]}"; do
        if $MYSQL_CMD $DB_NAME -e "DESCRIBE $table;" >/dev/null 2>&1; then
            # 获取记录数
            COUNT=$($MYSQL_CMD $DB_NAME -se "SELECT COUNT(*) FROM $table;")
            echo -e "${GREEN}✅ $table: 表存在，$COUNT 条记录${NC}"
        else
            echo -e "${RED}❌ $table: 表不存在${NC}"
            ALL_SUCCESS=false
        fi
    done
    
    if $ALL_SUCCESS; then
        return 0
    else
        return 1
    fi
}

# 显示表结构信息
show_table_info() {
    echo ""
    echo -e "${BLUE}📋 数据库表结构信息:${NC}"
    echo "===================="
    
    echo ""
    echo "用户表 (users):"
    echo "- 字段: user_id, username, password_hash, nickname, avatar_url"
    echo "- 索引: idx_username"
    echo "- 特点: UUID主键，用户名唯一"
    
    echo ""
    echo "情侣关系表 (couples):"
    echo "- 字段: couple_id, user1_id, user2_id, relationship_name, match_code, status"
    echo "- 索引: idx_match_code, idx_user1, idx_user2"
    echo "- 特点: 支持匹配码机制，状态管理"
    
    echo ""
    echo "心情记录表 (moods):"
    echo "- 字段: entry_id, user_id, couple_id, date, mood, note"
    echo "- 索引: unique_user_date, idx_couple_date, idx_updated_at, idx_sync_status"
    echo "- 特点: 软删除，同步状态，心情值约束(1-12)"
    
    echo ""
    echo "刷新令牌表 (refresh_tokens):"
    echo "- 字段: token_id, user_id, token_hash, device_id, expires_at"
    echo "- 索引: idx_user_device, idx_expires_at"
    echo "- 特点: 支持多设备，自动过期"
    
    echo ""
    echo "迁移记录表 (migrations):"
    echo "- 字段: id, version, name, executed_at"
    echo "- 特点: 跟踪数据库版本变更"
}

# 执行创建表操作
if create_tables; then
    echo -e "${GREEN}✅ 所有表创建完成${NC}"
    
    if verify_tables; then
        echo ""
        echo -e "${GREEN}🎉 数据库表创建成功！${NC}"
        show_table_info
        
        echo ""
        echo -e "${YELLOW}📝 注意事项:${NC}"
        echo "1. 所有表都使用 utf8mb4 字符集，支持完整的Unicode"
        echo "2. 心情值限制在1-12范围内"
        echo "3. 用户每天只能有一条心情记录"
        echo "4. 刷新令牌支持多设备管理"
        echo "5. 所有外键都设置了级联删除"
        
        echo ""
        echo -e "${BLUE}🔗 相关文档:${NC}"
        echo "- 数据库设计: docs/DATABASE.md"
        echo "- API文档: docs/API.md"
        echo "- 迁移脚本: migrations/"
        
    else
        echo -e "${RED}❌ 部分表创建失败，请检查错误信息${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ 表创建过程中出现错误${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}✨ 数据库初始化完成！${NC}"
