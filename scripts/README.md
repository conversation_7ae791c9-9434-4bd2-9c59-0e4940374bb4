# MoodTracker 数据库管理脚本

## 概述

本目录包含了 MoodTracker API 项目的数据库管理脚本，提供完整的数据库创建、验证和管理功能。所有脚本都基于 `docs/DATABASE.md` 文档设计，确保数据库结构的准确性和一致性。

## 脚本列表

### 🗄️ `database_manager.sh` - 数据库管理主脚本
**功能**: 提供交互式的数据库管理界面，整合所有数据库操作
**特点**:
- 交互式菜单操作
- 完整的数据库生命周期管理
- 安全的数据库连接配置
- 详细的操作反馈和错误处理

**使用方法**:
```bash
./scripts/database_manager.sh
```

### 🏗️ `create_database_tables.sh` - 数据库表创建脚本
**功能**: 根据DATABASE.md文档创建所有必需的数据库表
**特点**:
- 交互式输入数据库连接信息
- 完整的表结构创建（用户、情侣关系、心情记录、刷新令牌）
- 自动创建索引和外键约束
- 详细的创建过程反馈

**使用方法**:
```bash
./scripts/create_database_tables.sh
```

**创建的表**:
- `users` - 用户表
- `couples` - 情侣关系表  
- `moods` - 心情记录表
- `refresh_tokens` - 刷新令牌表
- `migrations` - 迁移记录表

### 🔍 `validate_database_schema.sh` - 数据库架构验证脚本
**功能**: 验证实际数据库结构与DATABASE.md文档的一致性
**特点**:
- 全面的表结构验证
- 字段类型和约束检查
- 索引和外键验证
- 详细的验证报告

**使用方法**:
```bash
./scripts/validate_database_schema.sh
```

**验证内容**:
- 表存在性检查
- 字段完整性验证
- 数据类型一致性
- 索引配置验证
- 外键约束检查

## 数据库结构

### 表结构概览

```
users (用户表)
├── user_id (VARCHAR(36), PRIMARY KEY)
├── username (VARCHAR(50), UNIQUE)
├── password_hash (VARCHAR(255))
├── nickname (VARCHAR(100))
├── avatar_url (VARCHAR(500))
├── created_at (TIMESTAMP)
├── updated_at (TIMESTAMP)
└── last_login_at (TIMESTAMP)

couples (情侣关系表)
├── couple_id (VARCHAR(36), PRIMARY KEY)
├── user1_id (VARCHAR(36), FK -> users.user_id)
├── user2_id (VARCHAR(36), FK -> users.user_id)
├── relationship_name (VARCHAR(100))
├── match_code (VARCHAR(8))
├── status (ENUM: 'waiting', 'matched')
├── created_at (TIMESTAMP)
└── matched_at (TIMESTAMP)

moods (心情记录表)
├── entry_id (VARCHAR(36), PRIMARY KEY)
├── user_id (VARCHAR(36), FK -> users.user_id)
├── couple_id (VARCHAR(36), FK -> couples.couple_id)
├── date (DATE)
├── mood (INT, 1-12)
├── note (TEXT)
├── created_at (TIMESTAMP)
├── updated_at (TIMESTAMP)
├── sync_status (ENUM: 'PENDING', 'SYNCED', 'FAILED')
└── is_deleted (BOOLEAN)

refresh_tokens (刷新令牌表)
├── token_id (VARCHAR(36), PRIMARY KEY)
├── user_id (VARCHAR(36), FK -> users.user_id)
├── token_hash (VARCHAR(255))
├── device_id (VARCHAR(36))
├── expires_at (TIMESTAMP)
└── created_at (TIMESTAMP)
```

### 索引配置

**用户表索引**:
- `idx_username` - 用户名索引

**情侣关系表索引**:
- `idx_match_code` - 匹配码索引
- `idx_user1` - 用户1索引
- `idx_user2` - 用户2索引

**心情记录表索引**:
- `unique_user_date` - 用户日期唯一索引
- `idx_couple_date` - 情侣日期索引
- `idx_updated_at` - 更新时间索引
- `idx_sync_status` - 同步状态索引

**刷新令牌表索引**:
- `idx_user_device` - 用户设备索引
- `idx_expires_at` - 过期时间索引

## 使用指南

### 快速开始

1. **创建数据库表**:
```bash
# 使用管理器（推荐）
./scripts/database_manager.sh
# 选择选项 1

# 或直接运行创建脚本
./scripts/create_database_tables.sh
```

2. **验证数据库结构**:
```bash
# 使用管理器
./scripts/database_manager.sh
# 选择选项 2

# 或直接运行验证脚本
./scripts/validate_database_schema.sh
```

### 数据库连接配置

所有脚本都支持交互式输入数据库连接信息：

- **主机地址** (默认: localhost)
- **端口号** (默认: 3306)
- **用户名** (默认: root)
- **密码** (安全输入，不显示)
- **数据库名** (默认: moodtracker)

### 环境要求

- **MySQL 5.7+** 或 **MySQL 8.0+**
- **Bash 4.0+**
- **mysql** 客户端工具
- 适当的数据库权限（CREATE, DROP, ALTER, INDEX）

## 安全注意事项

### 数据库权限
确保使用的数据库用户具有以下权限：
```sql
GRANT CREATE, DROP, ALTER, INDEX, SELECT, INSERT, UPDATE, DELETE 
ON moodtracker.* TO 'your_user'@'localhost';
```

### 密码安全
- 脚本使用安全的密码输入方式（不回显）
- 不会在命令历史中保存密码
- 建议使用专用的数据库用户

### 生产环境
- 在生产环境中谨慎使用清理功能
- 建议先在测试环境验证脚本
- 定期备份数据库

## 故障排除

### 常见问题

1. **连接失败**
```bash
❌ 数据库连接失败，请检查连接信息
```
**解决方案**:
- 检查MySQL服务是否运行
- 验证连接参数（主机、端口、用户名、密码）
- 确认数据库存在
- 检查防火墙设置

2. **权限不足**
```bash
❌ Access denied for user
```
**解决方案**:
- 检查用户权限
- 使用具有足够权限的用户
- 联系数据库管理员

3. **表已存在**
```bash
Table 'users' already exists
```
**解决方案**:
- 这是正常情况，脚本使用 `CREATE TABLE IF NOT EXISTS`
- 如需重建表，先清理数据库

4. **字符集问题**
```bash
Incorrect string value
```
**解决方案**:
- 确保数据库使用 utf8mb4 字符集
- 检查客户端连接字符集设置

### 调试模式

启用详细输出：
```bash
bash -x ./scripts/database_manager.sh
```

## 维护和更新

### 更新脚本
当DATABASE.md文档更新时：
1. 检查表结构变更
2. 更新相应的CREATE TABLE语句
3. 更新验证脚本的检查项
4. 测试脚本功能

### 版本控制
- 所有脚本变更都应提交到版本控制
- 重要变更需要更新此README
- 保持脚本与文档同步

## 相关文档

- [数据库设计文档](../docs/DATABASE.md)
- [API文档](../docs/API.md)
- [项目README](../README.md)
- [测试指南](../tests/README.md)

## 贡献指南

1. 修改脚本前先阅读DATABASE.md文档
2. 保持脚本的交互性和用户友好性
3. 添加适当的错误处理和验证
4. 更新相关文档和注释
5. 在多个环境中测试脚本

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](../LICENSE) 文件。
