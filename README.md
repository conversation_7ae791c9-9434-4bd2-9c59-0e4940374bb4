# MoodTracker API

一个用于情侣心情记录和同步的后端API系统，支持用户认证、情侣匹配、心情记录管理和数据同步功能。

## ✨ 功能特性

### 🔐 用户认证系统
- 用户注册和登录
- JWT访问令牌和刷新令牌
- 安全的密码加密存储
- 设备管理和多设备登录

### 💑 情侣匹配系统
- 创建情侣关系
- 8位数字匹配码
- 匹配码过期机制
- 情侣关系管理

### 📊 心情记录管理
- 每日心情记录（1-12级别）
- 心情备注和时间戳
- 软删除和数据保护
- 情侣双方记录查看

### 🔄 数据同步系统
- 增量数据同步
- 冲突检测和处理
- 批量数据上传
- 离线数据支持

### 🛡️ 安全和性能
- API限流保护
- CORS跨域支持
- 全局错误处理
- 结构化日志记录
- Panic自动恢复

## 🚀 技术栈

- **语言**: Go 1.21+
- **框架**: Gin Web框架
- **数据库**: MySQL 8.0+
- **认证**: JWT (golang-jwt/jwt/v5)
- **日志**: Logrus结构化日志
- **测试**: Go原生测试框架
- **部署**: Docker + Docker Compose

## 📋 API概览

| 功能模块 | 端点 | 方法 | 描述 |
|---------|------|------|------|
| 认证 | `/api/v1/auth/register` | POST | 用户注册 |
| 认证 | `/api/v1/auth/login` | POST | 用户登录 |
| 认证 | `/api/v1/auth/refresh-token` | POST | 刷新令牌 |
| 认证 | `/api/v1/auth/logout` | POST | 用户登出 |
| 用户 | `/api/v1/user/me` | GET | 获取当前用户信息 |
| 情侣 | `/api/v1/couples/create` | POST | 创建情侣关系 |
| 情侣 | `/api/v1/couples/join` | POST | 加入情侣关系 |
| 情侣 | `/api/v1/couples/info` | GET | 获取情侣信息 |
| 情侣 | `/api/v1/couples/leave` | DELETE | 离开情侣关系 |
| 心情 | `/api/v1/moods` | POST | 创建心情记录 |
| 心情 | `/api/v1/moods/{entryId}` | PUT | 更新心情记录 |
| 心情 | `/api/v1/moods/{entryId}` | DELETE | 删除心情记录 |
| 心情 | `/api/v1/moods/couple` | GET | 获取情侣心情记录 |
| 同步 | `/api/v1/sync/changes` | GET | 获取增量变更 |
| 同步 | `/api/v1/sync/upload` | POST | 批量上传变更 |
| 同步 | `/api/v1/sync/last-sync-time` | GET | 获取最后同步时间 |

## 🏃‍♂️ 快速开始

### 环境要求

- Go 1.21或更高版本
- MySQL 8.0或更高版本

### 安装和运行

1. **克隆项目**
```bash
git clone <repository-url>
cd MoodTrackerAPI
```

2. **安装依赖**
```bash
go mod tidy
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，配置数据库连接等信息
```

4. **运行数据库迁移**
```bash
go run cmd/migrate/main.go
```

5. **启动服务**
```bash
go run cmd/server/main.go
```

服务将在 http://localhost:8080 启动

### 🐳 Docker 部署

```bash
# 使用 Docker Compose 一键部署
docker-compose up -d

# 运行数据库迁移
docker-compose exec api ./migrate
```

## 📚 文档

- [API文档](docs/API.md) - 详细的API接口文档
- [部署指南](docs/DEPLOYMENT.md) - 生产环境部署指南
- [开发文档](dev_docs/) - 开发过程文档

## 🧪 测试

### 运行所有测试
```bash
go test ./... -v
```

### 运行特定模块测试
```bash
# 工具函数测试
go test ./pkg/utils/... -v

# 中间件测试
go test ./internal/middleware/... -v
```

### 测试覆盖率
```bash
go test ./... -cover
```

## 🏗️ 项目结构

```
MoodTrackerAPI/
├── cmd/                    # 应用程序入口
│   ├── server/            # 主服务器
│   └── migrate/           # 数据库迁移工具
├── internal/              # 内部包
│   ├── handlers/          # HTTP处理器
│   ├── services/          # 业务逻辑层
│   ├── models/            # 数据模型
│   ├── database/          # 数据库相关
│   └── middleware/        # 中间件
├── pkg/                   # 公共包
│   ├── response/          # 响应格式
│   └── utils/             # 工具函数
├── config/                # 配置管理
├── migrations/            # 数据库迁移文件
├── docs/                  # 文档
├── dev_docs/              # 开发文档
└── tests/                 # 测试文件
```

## 🔧 开发特性

### 中间件系统
- **日志中间件**: 结构化请求日志
- **CORS中间件**: 跨域资源共享
- **限流中间件**: API访问频率控制
- **恢复中间件**: Panic自动恢复
- **错误处理**: 统一错误响应格式

### 安全特性
- bcrypt密码加密
- JWT令牌认证
- API限流保护
- 输入参数验证
- SQL注入防护

### 性能优化
- 数据库连接池
- 令牌桶限流算法
- 增量数据同步
- 软删除机制
- 索引优化

## 📊 监控和日志

### 健康检查
```bash
curl http://localhost:8080/health
```

### 日志级别
- `debug`: 调试信息
- `info`: 一般信息  
- `warn`: 警告信息
- `error`: 错误信息

### 监控指标
- HTTP请求数量和响应时间
- 数据库连接状态
- 限流触发统计
- 错误率统计

## 🤝 贡献

欢迎提交Issue和Pull Request！

### 开发流程
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 🙏 致谢

感谢所有开源项目的贡献者，特别是：
- [Gin](https://github.com/gin-gonic/gin) - HTTP Web框架
- [Logrus](https://github.com/sirupsen/logrus) - 结构化日志
- [JWT-Go](https://github.com/golang-jwt/jwt) - JWT实现
