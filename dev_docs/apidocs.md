# MoodTracker API 接口文档

## 基础信息

### 服务器信息
- **基础URL**: `https://your-api-domain.com/api/v1`
- **协议**: HTTPS (生产环境) / HTTP (开发环境)
- **数据格式**: JSON
- **字符编码**: UTF-8

### 数据库连接信息
```go
// 数据库配置
const (
    DB_HOST     = "mysql3.sqlpub.com"
    DB_PORT     = 3308
    DB_NAME     = "moodtracker"
    DB_USERNAME = "syferie"
    DB_PASSWORD = "ZUFDEILUXjppsQ2p"
)
```

### 通用响应格式
```json
{
    "success": true,
    "message": "操作成功",
    "data": {},
    "error": null,
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### 错误响应格式
```json
{
    "success": false,
    "message": null,
    "data": null,
    "error": "具体错误信息",
    "timestamp": "2024-01-01T12:00:00Z"
}
```

## 认证系统 (Authentication)

### 1. 用户注册
**POST** `/auth/register`

#### 请求体
```json
{
    "username": "testuser",
    "password": "password123",
    "nickname": "测试用户"
}
```

#### 响应体
```json
{
    "success": true,
    "data": {
        "user": {
            "userId": "uuid-string",
            "username": "testuser",
            "nickname": "测试用户",
            "avatarUrl": null,
            "createdAt": "2024-01-01T12:00:00Z",
            "lastLoginAt": null
        },
        "accessToken": "jwt-token-string",
        "refreshToken": "refresh-token-string",
        "expiresAt": "2024-01-01T13:00:00Z"
    }
}
```

#### 注意事项
- 用户名必须唯一，3-20个字符，只能包含字母、数字、下划线
- 密码至少6个字符，最多50个字符
- 密码需要使用bcrypt加密存储
- JWT token有效期建议1小时
- 需要生成设备ID并关联用户

### 2. 用户登录
**POST** `/auth/login`

#### 请求体
```json
{
    "username": "testuser",
    "password": "password123",
    "deviceId": "device-uuid-string"
}
```

#### 响应体
```json
{
    "success": true,
    "data": {
        "user": {
            "userId": "uuid-string",
            "username": "testuser",
            "nickname": "测试用户",
            "avatarUrl": null,
            "createdAt": "2024-01-01T12:00:00Z",
            "lastLoginAt": "2024-01-01T12:00:00Z"
        },
        "accessToken": "jwt-token-string",
        "refreshToken": "refresh-token-string",
        "expiresAt": "2024-01-01T13:00:00Z"
    }
}
```

#### 注意事项
- 需要验证用户名和密码
- 更新lastLoginAt字段
- 如果设备ID不存在，需要创建新的设备记录

### 3. 刷新Token
**POST** `/auth/refresh-token`

#### 请求头
```
Authorization: Bearer <refresh-token>
```

#### 响应体
```json
{
    "success": true,
    "data": {
        "accessToken": "new-jwt-token-string",
        "refreshToken": "new-refresh-token-string",
        "expiresAt": "2024-01-01T14:00:00Z"
    }
}
```

### 4. 用户登出
**POST** `/auth/logout`

#### 请求头
```
Authorization: Bearer <access-token>
```

#### 响应体
```json
{
    "success": true,
    "data": null
}
```

#### 注意事项
- 需要将token加入黑名单或删除refresh token
- 即使token无效也应该返回成功

## 情侣匹配系统 (Couples)

### 1. 创建情侣关系
**POST** `/couples/create`

#### 请求头
```
Authorization: Bearer <access-token>
```

#### 请求体
```json
{
    "relationshipName": "我们的小日子"
}
```

#### 响应体
```json
{
    "success": true,
    "data": {
        "coupleId": "couple-uuid-string",
        "matchCode": "12345678",
        "user1": {
            "userId": "uuid-string",
            "username": "user1",
            "nickname": "用户1",
            "avatarUrl": null,
            "createdAt": "2024-01-01T12:00:00Z",
            "lastLoginAt": "2024-01-01T12:00:00Z"
        },
        "user2": null,
        "relationshipName": "我们的小日子",
        "status": "waiting",
        "createdAt": "2024-01-01T12:00:00Z",
        "matchedAt": null
    }
}
```

#### 注意事项
- 一个用户只能创建一个情侣关系
- 匹配码为8位随机数字
- 匹配码有效期1小时
- 状态为"waiting"表示等待匹配

### 2. 加入情侣关系
**POST** `/couples/join`

#### 请求头
```
Authorization: Bearer <access-token>
```

#### 请求体
```json
{
    "matchCode": "12345678"
}
```

#### 响应体
```json
{
    "success": true,
    "data": {
        "coupleId": "couple-uuid-string",
        "matchCode": null,
        "user1": {
            "userId": "uuid-string-1",
            "username": "user1",
            "nickname": "用户1",
            "avatarUrl": null,
            "createdAt": "2024-01-01T12:00:00Z",
            "lastLoginAt": "2024-01-01T12:00:00Z"
        },
        "user2": {
            "userId": "uuid-string-2",
            "username": "user2",
            "nickname": "用户2",
            "avatarUrl": null,
            "createdAt": "2024-01-01T12:00:00Z",
            "lastLoginAt": "2024-01-01T12:00:00Z"
        },
        "relationshipName": "我们的小日子",
        "status": "matched",
        "createdAt": "2024-01-01T12:00:00Z",
        "matchedAt": "2024-01-01T12:30:00Z"
    }
}
```

#### 注意事项
- 验证匹配码是否有效且未过期
- 一个用户只能加入一个情侣关系
- 匹配成功后状态变为"matched"
- 设置matchedAt时间，清空matchCode

### 3. 获取情侣关系信息
**GET** `/couples/info`

#### 请求头
```
Authorization: Bearer <access-token>
```

#### 响应体
```json
{
    "success": true,
    "data": {
        "coupleId": "couple-uuid-string",
        "matchCode": null,
        "user1": {
            "userId": "uuid-string-1",
            "username": "user1",
            "nickname": "用户1",
            "avatarUrl": null,
            "createdAt": "2024-01-01T12:00:00Z",
            "lastLoginAt": "2024-01-01T12:00:00Z"
        },
        "user2": {
            "userId": "uuid-string-2",
            "username": "user2",
            "nickname": "用户2",
            "avatarUrl": null,
            "createdAt": "2024-01-01T12:00:00Z",
            "lastLoginAt": "2024-01-01T12:00:00Z"
        },
        "relationshipName": "我们的小日子",
        "status": "matched",
        "createdAt": "2024-01-01T12:00:00Z",
        "matchedAt": "2024-01-01T12:30:00Z"
    }
}
```

#### 注意事项
- 如果用户没有情侣关系，data为null
- 只返回当前用户所属的情侣关系

### 4. 离开情侣关系
**DELETE** `/couples/leave`

#### 请求头
```
Authorization: Bearer <access-token>
```

#### 响应体
```json
{
    "success": true,
    "data": null
}
```

#### 注意事项
- 删除情侣关系记录
- 需要同时删除相关的心情记录或标记为已删除
- 两个用户都会失去情侣关系

## 心情记录系统 (Moods)

### 1. 创建心情记录
**POST** `/moods`

#### 请求头
```
Authorization: Bearer <access-token>
```

#### 请求体
```json
{
    "date": "2024-01-01",
    "mood": 8,
    "note": "今天心情很好！"
}
```

#### 响应体
```json
{
    "success": true,
    "data": {
        "entryId": "entry-uuid-string",
        "userId": "user-uuid-string",
        "coupleId": "couple-uuid-string",
        "date": "2024-01-01",
        "mood": 8,
        "note": "今天心情很好！",
        "createdAt": "2024-01-01T12:00:00Z",
        "updatedAt": "2024-01-01T12:00:00Z",
        "syncStatus": "SYNCED",
        "isDeleted": false
    }
}
```

#### 注意事项
- mood值范围：1-12 (对应12种心情等级)
- 每个用户每天只能有一条心情记录
- 如果当天已有记录，应该更新而不是创建新记录
- 需要验证用户是否有情侣关系

### 2. 更新心情记录
**PUT** `/moods/{entryId}`

#### 请求头
```
Authorization: Bearer <access-token>
```

#### 请求体
```json
{
    "mood": 9,
    "note": "心情更好了！"
}
```

#### 响应体
```json
{
    "success": true,
    "data": {
        "entryId": "entry-uuid-string",
        "userId": "user-uuid-string",
        "coupleId": "couple-uuid-string",
        "date": "2024-01-01",
        "mood": 9,
        "note": "心情更好了！",
        "createdAt": "2024-01-01T12:00:00Z",
        "updatedAt": "2024-01-01T12:30:00Z",
        "syncStatus": "SYNCED",
        "isDeleted": false
    }
}
```

#### 注意事项
- 只能更新自己的心情记录
- 更新updatedAt字段
- 验证entryId是否存在且属于当前用户

### 3. 删除心情记录
**DELETE** `/moods/{entryId}`

#### 请求头
```
Authorization: Bearer <access-token>
```

#### 响应体
```json
{
    "success": true,
    "data": null
}
```

#### 注意事项
- 使用软删除，设置isDeleted=true
- 只能删除自己的心情记录
- 更新updatedAt字段

### 4. 获取情侣双方心情记录
**GET** `/moods/couple`

#### 请求头
```
Authorization: Bearer <access-token>
```

#### 查询参数
- `startDate`: 开始日期 (可选，格式: 2024-01-01)
- `endDate`: 结束日期 (可选，格式: 2024-01-31)
- `limit`: 限制数量 (可选，默认100)
- `offset`: 偏移量 (可选，默认0)

#### 响应体
```json
{
    "success": true,
    "data": [
        {
            "entryId": "entry-uuid-string-1",
            "userId": "user-uuid-string-1",
            "coupleId": "couple-uuid-string",
            "date": "2024-01-01",
            "mood": 8,
            "note": "今天心情很好！",
            "createdAt": "2024-01-01T12:00:00Z",
            "updatedAt": "2024-01-01T12:00:00Z",
            "syncStatus": "SYNCED",
            "isDeleted": false
        },
        {
            "entryId": "entry-uuid-string-2",
            "userId": "user-uuid-string-2",
            "coupleId": "couple-uuid-string",
            "date": "2024-01-01",
            "mood": 7,
            "note": "还不错的一天",
            "createdAt": "2024-01-01T14:00:00Z",
            "updatedAt": "2024-01-01T14:00:00Z",
            "syncStatus": "SYNCED",
            "isDeleted": false
        }
    ]
}
```

#### 注意事项
- 返回情侣双方的心情记录
- 按日期倒序排列
- 不返回已删除的记录 (isDeleted=false)
- 需要验证用户是否有情侣关系

## 数据同步系统 (Sync)

### 1. 获取增量变更
**GET** `/sync/changes`

#### 请求头
```
Authorization: Bearer <access-token>
```

#### 查询参数
- `lastSyncTime`: 上次同步时间 (可选，格式: 2024-01-01T12:00:00Z)

#### 响应体
```json
{
    "success": true,
    "data": {
        "changes": [
            {
                "entryId": "entry-uuid-string",
                "userId": "user-uuid-string",
                "coupleId": "couple-uuid-string",
                "date": "2024-01-01",
                "mood": 8,
                "note": "今天心情很好！",
                "createdAt": "2024-01-01T12:00:00Z",
                "updatedAt": "2024-01-01T12:30:00Z",
                "syncStatus": "SYNCED",
                "isDeleted": false
            }
        ],
        "lastSyncTime": "2024-01-01T12:30:00Z"
    }
}
```

#### 注意事项
- 返回指定时间后的所有变更
- 包括新增、修改、删除的记录
- 用于客户端增量同步

### 2. 批量上传变更
**POST** `/sync/upload`

#### 请求头
```
Authorization: Bearer <access-token>
```

#### 请求体
```json
{
    "changes": [
        {
            "entryId": "entry-uuid-string",
            "date": "2024-01-01",
            "mood": 8,
            "note": "今天心情很好！",
            "createdAt": "2024-01-01T12:00:00Z",
            "updatedAt": "2024-01-01T12:00:00Z",
            "isDeleted": false
        }
    ]
}
```

#### 响应体
```json
{
    "success": true,
    "data": {
        "processed": 1,
        "conflicts": [],
        "errors": []
    }
}
```

#### 注意事项
- 批量处理客户端的变更
- 处理冲突：服务器时间戳优先
- 返回处理结果和冲突信息

## 技术实现细节

### 1. 数据库表结构

#### users 表
```sql
CREATE TABLE users (
    user_id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    nickname VARCHAR(100),
    avatar_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP NULL,
    INDEX idx_username (username)
);
```

#### couples 表
```sql
CREATE TABLE couples (
    couple_id VARCHAR(36) PRIMARY KEY,
    user1_id VARCHAR(36) NOT NULL,
    user2_id VARCHAR(36),
    match_code VARCHAR(8),
    relationship_name VARCHAR(100),
    status ENUM('waiting', 'matched') DEFAULT 'waiting',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    matched_at TIMESTAMP NULL,
    FOREIGN KEY (user1_id) REFERENCES users(user_id),
    FOREIGN KEY (user2_id) REFERENCES users(user_id),
    INDEX idx_match_code (match_code),
    INDEX idx_user1 (user1_id),
    INDEX idx_user2 (user2_id)
);
```

#### moods 表
```sql
CREATE TABLE moods (
    entry_id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    couple_id VARCHAR(36) NOT NULL,
    date DATE NOT NULL,
    mood INT NOT NULL CHECK (mood >= 1 AND mood <= 12),
    note TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sync_status ENUM('PENDING', 'SYNCED', 'FAILED') DEFAULT 'SYNCED',
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (couple_id) REFERENCES couples(couple_id),
    UNIQUE KEY unique_user_date (user_id, date),
    INDEX idx_couple_date (couple_id, date),
    INDEX idx_updated_at (updated_at),
    INDEX idx_sync_status (sync_status)
);
```

#### refresh_tokens 表
```sql
CREATE TABLE refresh_tokens (
    token_id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    device_id VARCHAR(36),
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    INDEX idx_user_device (user_id, device_id),
    INDEX idx_expires_at (expires_at)
);
```

### 2. JWT Token 结构
```json
{
    "sub": "user-uuid-string",
    "username": "testuser",
    "iat": 1640995200,
    "exp": 1640998800,
    "device_id": "device-uuid-string"
}
```

### 3. 错误代码定义
```go
const (
    // 通用错误
    ErrInternalServer    = "INTERNAL_SERVER_ERROR"
    ErrInvalidRequest    = "INVALID_REQUEST"
    ErrUnauthorized      = "UNAUTHORIZED"
    ErrForbidden         = "FORBIDDEN"
    ErrNotFound          = "NOT_FOUND"

    // 认证错误
    ErrInvalidCredentials = "INVALID_CREDENTIALS"
    ErrUserExists        = "USER_ALREADY_EXISTS"
    ErrTokenExpired      = "TOKEN_EXPIRED"
    ErrTokenInvalid      = "TOKEN_INVALID"

    // 业务错误
    ErrCoupleExists      = "COUPLE_ALREADY_EXISTS"
    ErrInvalidMatchCode  = "INVALID_MATCH_CODE"
    ErrMatchCodeExpired  = "MATCH_CODE_EXPIRED"
    ErrMoodExists        = "MOOD_ALREADY_EXISTS"
    ErrInvalidMoodValue  = "INVALID_MOOD_VALUE"
)
```

### 4. 推荐的Go技术栈
```go
// 主要依赖
github.com/gin-gonic/gin           // Web框架
github.com/go-sql-driver/mysql     // MySQL驱动
github.com/golang-jwt/jwt/v5       // JWT处理
github.com/google/uuid             // UUID生成
golang.org/x/crypto/bcrypt         // 密码加密
github.com/go-playground/validator // 数据验证

// 可选依赖
github.com/joho/godotenv           // 环境变量
github.com/sirupsen/logrus         // 日志
github.com/swaggo/gin-swagger      // API文档
```

### 5. 环境变量配置
```bash
# 数据库配置
DB_HOST=mysql3.sqlpub.com
DB_PORT=3308
DB_NAME=moodtracker
DB_USERNAME=syferie
DB_PASSWORD=ZUFDEILUXjppsQ2p

# JWT配置
JWT_SECRET=your-super-secret-key
JWT_EXPIRES_IN=3600

# 服务器配置
PORT=8080
GIN_MODE=release
```

### 6. 中间件要求

#### 认证中间件
- 验证JWT token
- 解析用户信息
- 处理token过期

#### 日志中间件
- 记录请求日志
- 记录响应时间
- 记录错误信息

#### CORS中间件
- 允许跨域请求
- 设置允许的请求头
- 处理预检请求

#### 限流中间件
- 防止API滥用
- 按IP或用户限流
- 返回429状态码

### 7. 部署建议

#### 开发环境
- 使用 `go run main.go`
- 连接开发数据库
- 启用详细日志

#### 生产环境
- 编译为二进制文件
- 使用Docker容器化
- 配置反向代理 (Nginx)
- 启用HTTPS
- 配置监控和日志收集

### 8. 测试要求

#### 单元测试
- 测试所有业务逻辑
- 模拟数据库操作
- 覆盖率 > 80%

#### 集成测试
- 测试完整API流程
- 使用测试数据库
- 验证数据一致性

#### 性能测试
- 并发用户测试
- 响应时间要求 < 200ms
- 数据库连接池优化

这份文档应该能帮助你快速开始Go API的开发。有任何问题随时问我！
