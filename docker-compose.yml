version: '3.8'

services:
  # MoodTracker API 服务
  api:
    image: syferie/moodtracker  # 使用你推送的镜像
    container_name: moodtracker-api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      # 服务器配置
      - SERVER_PORT=8080
      - SERVER_GIN_MODE=release

      # 数据库配置 (连接你的MySQL，请修改这些值)
      - DB_HOST=localhost
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=your_mysql_password
      - DB_NAME=moodtracker

      # JWT配置 (生产环境请修改密钥)
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - JWT_EXPIRES_IN=3600

      # 日志配置
      - LOG_LEVEL=info

      # 时区
      - TZ=Asia/Shanghai
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
